# MotionStreamer Blender插件项目结构

本文档描述了MotionStreamer Blender插件项目的完整结构和各文件的作用。

## 项目文件结构

```
MotionStreamer-main/
├── 📁 blender_addon/                    # Blender插件目录
│   └── __init__.py                      # 插件主文件
├── 📄 websocket_server.py               # WebSocket服务器
├── 📄 start_server.py                   # 服务器启动脚本
├── 📄 start_server.bat                  # Windows批处理启动脚本
├── 📄 install_requirements.py           # 依赖安装脚本
├── 📄 test_client.py                    # WebSocket客户端测试工具
├── 📄 README_BlenderPlugin.md           # 插件使用指南
├── 📄 PROJECT_STRUCTURE.md              # 项目结构说明 (本文件)
└── 📄 motionstreamer_server.log         # 服务器日志文件 (运行时生成)
```

## 文件详细说明

### 🔧 核心文件

#### `websocket_server.py`
- **作用**: WebSocket服务器核心实现
- **功能**: 
  - 加载MotionStreamer模型
  - 处理文本到动作生成请求
  - 提供WebSocket API接口
  - 支持位置和旋转两种动作模式
- **依赖**: torch, websockets, sentence-transformers等
- **启动**: `python websocket_server.py`

#### `blender_addon/__init__.py`
- **作用**: Blender插件主文件
- **功能**:
  - 提供用户界面面板
  - WebSocket客户端通信
  - MMD模型动作应用
  - 动画播放控制
- **安装**: 在Blender中作为插件安装

### 🚀 启动脚本

#### `start_server.py`
- **作用**: 增强的服务器启动脚本
- **功能**:
  - 环境检查和验证
  - 友好的启动界面
  - 命令行参数支持
  - 错误处理和日志
- **用法**: `python start_server.py [选项]`
- **选项**:
  - `--host`: 服务器地址 (默认: localhost)
  - `--port`: 端口号 (默认: 8765)
  - `--no-check`: 跳过环境检查
  - `--verbose`: 详细日志输出

#### `start_server.bat`
- **作用**: Windows批处理启动脚本
- **功能**: 
  - 一键启动服务器
  - 自动检查Python环境
  - 用户友好的界面
- **用法**: 双击运行或在命令行执行

### 🔧 工具脚本

#### `install_requirements.py`
- **作用**: 依赖包安装和环境检查工具
- **功能**:
  - 检查Python版本
  - 安装必需的Python包
  - 验证模型文件完整性
  - CUDA支持检查
- **用法**: `python install_requirements.py`

#### `test_client.py`
- **作用**: WebSocket客户端测试工具
- **功能**:
  - 连接测试
  - 单次动作生成测试
  - 交互模式测试
  - 批量测试
- **用法**: `python test_client.py [选项]`
- **模式**:
  - `--mode test`: 单次测试
  - `--mode interactive`: 交互模式
  - `--mode batch`: 批量测试

### 📚 文档文件

#### `README_BlenderPlugin.md`
- **作用**: 完整的用户使用指南
- **内容**:
  - 安装步骤
  - 使用方法
  - 界面说明
  - 故障排除
  - 技术细节

#### `PROJECT_STRUCTURE.md`
- **作用**: 项目结构说明文档 (本文件)
- **内容**: 文件结构和各组件说明

## 工作流程

### 1. 环境准备
```bash
# 1. 检查和安装依赖
python install_requirements.py

# 2. 验证环境
python start_server.py --no-check
```

### 2. 启动服务器
```bash
# 方式1: 使用启动脚本 (推荐)
python start_server.py

# 方式2: 直接启动
python websocket_server.py

# 方式3: Windows批处理
start_server.bat
```

### 3. 安装Blender插件
1. 打开Blender
2. Edit > Preferences > Add-ons
3. Install... > 选择 `blender_addon` 文件夹
4. 启用 "MotionStreamer MMD Driver" 插件

### 4. 使用插件
1. 在Blender 3D视图侧边栏找到MotionStreamer面板
2. 连接到WebSocket服务器
3. 输入动作描述文本
4. 生成并播放动作

## 技术架构

### 通信协议
- **协议**: WebSocket (JSON格式)
- **端口**: 8765 (默认)
- **消息格式**:
  ```json
  // 请求
  {
    "command": "generate_motion",
    "text": "A person is walking",
    "mode": "pos"
  }
  
  // 响应
  {
    "status": "success",
    "motion_data": [...],
    "frames": 120,
    "joints": 22
  }
  ```

### 数据流
1. **Blender插件** → WebSocket请求 → **服务器**
2. **服务器** → 文本编码 → **T5模型**
3. **T5模型** → 特征向量 → **LLaMA模型**
4. **LLaMA模型** → 动作潜在表示 → **TAE解码器**
5. **TAE解码器** → 动作数据 → **服务器**
6. **服务器** → WebSocket响应 → **Blender插件**
7. **Blender插件** → 动作应用 → **MMD模型**

### 模型组件
- **T5模型**: 文本编码 (`sentencet5-xxl/`)
- **LLaMA模型**: 序列生成 (`Experiments/latest.pth`)
- **TAE模型**: 动作编解码 (`Causal_TAE/net_last.pth`)
- **参考数据**: 动作结束状态 (`reference_end_latent_t2m_272.npy`)
- **标准化数据**: 均值和标准差 (`humanml3d_272/mean_std/`)

## 扩展开发

### 添加新功能
1. **服务器端**: 在 `websocket_server.py` 中添加新的命令处理
2. **插件端**: 在 `blender_addon/__init__.py` 中添加新的操作符和面板
3. **协议**: 更新通信协议格式

### 自定义骨骼映射
修改 `apply_position_data()` 函数中的 `joint_mapping` 字典:
```python
joint_mapping = {
    0: "自定义骨骼名1",
    1: "自定义骨骼名2",
    # ...
}
```

### 添加新的动作模式
1. 在服务器的 `generate_motion()` 方法中添加新模式处理
2. 在插件的 `motion_mode` 属性中添加新选项
3. 实现对应的数据处理函数

## 日志和调试

### 服务器日志
- **文件**: `motionstreamer_server.log`
- **级别**: INFO, ERROR
- **内容**: 连接状态, 生成请求, 错误信息

### Blender控制台
- **位置**: Window > Toggle System Console
- **内容**: 插件运行状态, 错误信息

### 调试模式
```bash
# 启用详细日志
python start_server.py --verbose

# 测试连接
python test_client.py --mode test

# 交互测试
python test_client.py --mode interactive
```

## 性能优化

### 服务器端
- 使用GPU加速 (CUDA)
- 模型预加载和缓存
- 异步处理多客户端

### 插件端
- 动作数据缓存
- 异步WebSocket通信
- 批量关键帧设置

### 系统要求
- **最低**: CPU, 8GB RAM, Python 3.8+
- **推荐**: GPU (CUDA), 16GB RAM, Python 3.9+
- **最佳**: RTX 3080+, 32GB RAM, Python 3.10+
