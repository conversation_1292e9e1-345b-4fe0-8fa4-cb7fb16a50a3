import asyncio
import websockets
import json
import torch
import numpy as np
import os
import sys
import logging
import time
from datetime import datetime
from models.llama_model import LLaMAHF, LLaMAHFConfig
import models.tae as tae
import options.option_transformer as option_trans
from sentence_transformers import SentenceTransformer
from visualization.recover_visualize import recover_from_local_position
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('motionstreamer_server.log', encoding='utf-8')
    ]
)

class MotionStreamerServer:
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        self.connected_clients = set()
        self.generation_count = 0
        self.setup_models()
        
    def setup_models(self):
        """初始化模型"""
        self.logger.info("正在初始化模型...")
        start_time = time.time()

        # 获取参数
        args = option_trans.get_args_parser()
        torch.manual_seed(args.seed)
        
        # 加载T5模型
        self.t5_model = SentenceTransformer('sentencet5-xxl/')
        self.t5_model.eval()
        for p in self.t5_model.parameters():
            p.requires_grad = False
            
        # 设置网络参数
        clip_range = [-30, 20]
        
        # 初始化TAE网络
        self.net = tae.Causal_HumanTAE(
            hidden_size=args.hidden_size,
            down_t=args.down_t,
            stride_t=args.stride_t,
            depth=args.depth,
            dilation_growth_rate=args.dilation_growth_rate,
            activation='relu',
            latent_dim=args.latent_dim,
            clip_range=clip_range
        )
        
        # 初始化Transformer
        config = LLaMAHFConfig.from_name('Normal_size')
        config.block_size = 78
        self.trans_encoder = LLaMAHF(config, args.num_diffusion_head_layers, args.latent_dim, self.device)
        
        # 加载检查点
        self.logger.info(f'正在加载检查点: {args.resume_pth}')
        ckpt = torch.load(args.resume_pth, map_location='cpu')
        self.net.load_state_dict(ckpt['net'], strict=True)
        self.net.eval()
        self.net.to(self.device)

        if args.resume_trans is not None:
            self.logger.info(f'正在加载transformer检查点: {args.resume_trans}')
            ckpt = torch.load(args.resume_trans, map_location='cpu')
            new_ckpt_trans = {}
            for key in ckpt['trans'].keys():
                if key.split('.')[0] == 'module':
                    new_key = '.'.join(key.split('.')[1:])
                else:
                    new_key = key
                new_ckpt_trans[new_key] = ckpt['trans'][key]
            self.trans_encoder.load_state_dict(new_ckpt_trans, strict=True)

        self.trans_encoder.eval()
        self.trans_encoder.to(self.device)

        # 加载参考数据
        self.reference_end_latent = np.load('reference_end_latent_t2m_272.npy')
        self.reference_end_latent = torch.from_numpy(self.reference_end_latent).to(self.device)

        # 加载均值和标准差
        self.mean = np.load('humanml3d_272/mean_std/Mean.npy')
        self.std = np.load('humanml3d_272/mean_std/Std.npy')

        elapsed_time = time.time() - start_time
        self.logger.info(f"模型初始化完成! 耗时: {elapsed_time:.2f}秒")
        self.logger.info(f"使用设备: {self.device}")
        
    async def generate_motion_streaming(self, text, websocket, mode='pos'):
        """流式生成动作，实时发送每帧动作数据"""
        try:
            self.generation_count += 1
            self.logger.info(f"[#{self.generation_count}] 开始流式生成动作: '{text}' (模式: {mode})")
            start_time = time.time()
            first_frame_time = None  # 记录首帧生成时间

            # 发送开始生成状态
            await websocket.send(json.dumps({
                'status': 'streaming_start',
                'message': '开始流式生成动作...',
                'generation_id': self.generation_count
            }))

            # 使用自定义的流式生成方法
            threshold = 0.1
            motion_tokens = []
            all_frames = []  # 存储所有生成的帧数据
            
            async for token_data in self.stream_generate_tokens(text, threshold, websocket, mode):
                motion_tokens.append(token_data['token'])
                
                # 如果token_data包含帧数据，则发送
                if 'frame_data' in token_data:
                    frame_data = token_data['frame_data']
                    all_frames.extend(frame_data)
                    
                    # 记录首帧生成时间
                    if first_frame_time is None and len(all_frames) > 0:
                        first_frame_time = time.time()
                        first_frame_elapsed = first_frame_time - start_time
                        self.logger.info(f"[#{self.generation_count}] 首帧生成完成! 耗时: {first_frame_elapsed:.3f}秒")
                    
                    # 发送实时帧数据
                    frame_message = {
                        'status': 'streaming_frames',
                        'generation_id': self.generation_count,
                        'current_token': len(motion_tokens),
                        'progress_percent': token_data.get('progress', 0),
                        'new_frames': frame_data,
                        'total_frames': len(all_frames),
                        'message': f'已生成 {len(all_frames)} 帧动作数据',
                        'timestamp': time.time()
                    }
                    
                    # 如果是首帧，添加首帧耗时信息
                    if first_frame_time is not None and len(all_frames) == len(frame_data):
                        frame_message['first_frame_time'] = first_frame_elapsed
                    
                    # 检查WebSocket连接状态
                    try:
                        if websocket.closed:
                            self.logger.warning(f"[#{self.generation_count}] WebSocket连接已关闭，停止生成")
                            return False
                    except AttributeError:
                        # 某些websocket实现可能没有closed属性，使用其他方法检查
                        pass
                        
                    await websocket.send(json.dumps(frame_message))
                
                # 优化异步让出，每帧发送时减少延迟
                await asyncio.sleep(0.001)  # 1ms延迟，平衡实时性和系统负载

            elapsed_time = time.time() - start_time
            self.logger.info(f"[#{self.generation_count}] 流式动作生成完成! 耗时: {elapsed_time:.2f}秒, 总帧数: {len(all_frames)}")
            
            # 检查WebSocket连接状态
            try:
                if websocket.closed:
                    self.logger.warning(f"[#{self.generation_count}] WebSocket连接已关闭，无法发送最终结果")
                    return False
            except AttributeError:
                # 某些websocket实现可能没有closed属性，使用其他方法检查
                pass
            
            # 发送最终完成状态
            completion_message = {
                'status': 'streaming_complete',
                'generation_id': self.generation_count,
                'text': text,
                'mode': mode,
                'total_frames': len(all_frames),
                'joints': 22 if mode == 'pos' else 272,
                'total_time': elapsed_time,
                'message': '动作生成完成'
            }
            
            # 添加首帧耗时信息
            if first_frame_time is not None:
                completion_message['first_frame_time'] = first_frame_elapsed
                
            await websocket.send(json.dumps(completion_message))
            
            return True

        except websockets.exceptions.ConnectionClosed as e:
            self.logger.warning(f"[#{self.generation_count}] WebSocket连接已关闭: {e}")
            return False
        except Exception as e:
            self.logger.error(f"[#{self.generation_count}] 流式生成动作时出错: {e}")
            try:
                if not getattr(websocket, 'closed', False):
                    await websocket.send(json.dumps({
                        'status': 'streaming_error',
                        'generation_id': self.generation_count,
                        'message': f'生成失败: {str(e)}'
                    }))
            except:
                pass  # 忽略发送错误消息时的异常
            return False

    async def stream_generate_tokens(self, text, threshold=0.1, websocket=None, mode='pos'):
        """流式生成动作tokens的异步生成器，实时解码并返回帧数据"""
        try:
            # 准备文本特征
            feat_text = torch.from_numpy(self.t5_model.encode(text)).float()
            feat_text = feat_text.to(self.device)
            
            # 准备空文本特征用于CFG
            empty_text = ''
            empty_feat_text = torch.from_numpy(self.t5_model.encode(empty_text)).float()
            empty_feat_text = empty_feat_text.unsqueeze(0)
            empty_feat_text = empty_feat_text.to(self.device)
            
            # 流式生成参数
            max_token_len = 312 // 4  # 78 tokens
            cfg = 4.0
            temperature = 1.0
            
            # 用于累积tokens进行解码
            accumulated_tokens = []
            last_decoded_frames = 0  # 记录上次解码的帧数，用于增量发送
            
            for k in range(max_token_len):
                if k == 0:
                    x = []
                else:
                    x = xs
                
                # 前向推理
                conditions = self.trans_encoder.forward_inference(x, feat_text)
                conditions = conditions[:, -1, :]
                
                empty_conditions = self.trans_encoder.forward(x, empty_feat_text)
                empty_conditions = empty_conditions[:, -1, :]
                
                # CFG采样
                mix_conditions = torch.cat([conditions, empty_conditions], dim=0)
                sampled_token_latent = self.trans_encoder.diff_loss.sample(
                    mix_conditions, temperature=temperature, cfg=cfg
                )
                
                # 分离CFG结果
                scaled_logits, _ = sampled_token_latent.chunk(2, dim=0)
                scaled_logits = scaled_logits.unsqueeze(0)
                
                if k == 0:
                    xs = scaled_logits
                else:
                    xs = torch.cat((xs, scaled_logits), dim=1)
                
                # 累积token用于解码
                accumulated_tokens.append(scaled_logits)
                
                # 优化解码策略：每个token都解码以获得最佳实时性
                decode_interval = 1  # 每1个token解码一次，实现每帧发送
                should_decode = (k + 1) % decode_interval == 0 or k == max_token_len - 1
                
                frame_data = None
                if should_decode and len(accumulated_tokens) > 0:
                    try:
                        # 将累积的tokens组合成motion_latents
                        current_motion_latents = torch.cat(accumulated_tokens, dim=1)
                        
                        # 使用torch.no_grad()优化内存使用
                        with torch.no_grad():
                            # 解码当前的动作序列
                            motion_seqs = self.net.forward_decoder(current_motion_latents)
                            motion = motion_seqs.squeeze(0)
                            motion = motion.detach().cpu().numpy()
                            
                            if mode == 'pos':
                                # 恢复关节位置
                                pred_xyz = recover_from_local_position(motion * self.std + self.mean, 22)
                                all_frames = pred_xyz.tolist()
                            else:
                                # 返回原始272维表示
                                all_frames = motion.tolist()
                            
                            # 只发送新增的帧数据，避免重复发送
                            if len(all_frames) > last_decoded_frames:
                                frame_data = all_frames[last_decoded_frames:]
                                last_decoded_frames = len(all_frames)
                            
                    except Exception as decode_error:
                        self.logger.warning(f"解码token时出错: {decode_error}")
                        frame_data = None
                
                # 检查是否应该提前停止
                distance = None
                if self.reference_end_latent is not None and k > 5:
                    current_token = scaled_logits.squeeze(0)
                    distance = torch.norm(current_token - self.reference_end_latent, dim=-1)
                    if distance < threshold:
                        self.logger.info(f"提前停止生成，距离阈值: {distance.item():.4f} < {threshold}")
                        break
                
                # 计算进度
                progress = min(100, (k + 1) / max_token_len * 100)
                
                # 返回当前token和帧数据
                result = {
                    'token': scaled_logits,
                    'token_index': k,
                    'progress': progress,
                    'distance': distance.item() if distance is not None else None
                }
                
                # 如果有新的帧数据，添加到结果中
                if frame_data is not None and len(frame_data) > 0:
                    result['frame_data'] = frame_data
                
                yield result
                
                # 减少延迟以提高响应性，每帧发送时进一步优化
                await asyncio.sleep(0.0001)  # 0.1ms延迟，最大化实时性
                
        except Exception as e:
            self.logger.error(f"流式生成tokens时出错: {e}")
            raise

    def generate_motion(self, text, mode='pos'):
        """保留原有的同步生成方法作为备用"""
        try:
            self.generation_count += 1
            self.logger.info(f"[#{self.generation_count}] 正在生成动作: '{text}' (模式: {mode})")
            start_time = time.time()

            # 生成动作潜在表示
            threshold = 0.1
            motion_latents = self.trans_encoder.sample_for_eval_CFG_inference(
                text=text,
                tokenizer=self.t5_model,
                device=self.device,
                reference_end_latent=self.reference_end_latent,
                threshold=threshold
            )

            # 解码动作序列
            motion_seqs = self.net.forward_decoder(motion_latents)
            motion = motion_seqs.squeeze(0)
            motion = motion.detach().cpu().numpy()

            if mode == 'pos':
                # 恢复关节位置
                pred_xyz = recover_from_local_position(motion * self.std + self.mean, 22)
                result = pred_xyz.tolist()
                data_shape = f"{len(result)} frames, 22 joints, 3D positions"
            else:
                # 返回原始272维表示
                result = motion.tolist()
                data_shape = f"{len(result)} frames, 272 dimensions"

            elapsed_time = time.time() - start_time
            self.logger.info(f"[#{self.generation_count}] 动作生成完成! 耗时: {elapsed_time:.2f}秒, 数据: {data_shape}")
            return result

        except Exception as e:
            self.logger.error(f"[#{self.generation_count}] 生成动作时出错: {e}")
            return None
    
    async def handle_client(self, websocket):
        """处理客户端连接"""
        print(f"客户端已连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    command = data.get('command')
                    
                    if command == 'generate_motion':
                        text = data.get('text', '')
                        mode = data.get('mode', 'pos')
                        streaming = data.get('streaming', True)  # 默认使用流式生成
                        
                        if not text:
                            await websocket.send(json.dumps({
                                'status': 'error',
                                'message': '文本不能为空'
                            }))
                            continue
                        
                        if streaming:
                            # 使用流式生成
                            await self.generate_motion_streaming(text, websocket, mode)
                        else:
                            # 使用传统的一次性生成
                            await websocket.send(json.dumps({
                                'status': 'generating',
                                'message': '正在生成动作，请稍候...'
                            }))
                            
                            # 生成动作
                            motion_data = self.generate_motion(text, mode)
                            
                            if motion_data is not None:
                                response = {
                                    'status': 'success',
                                    'text': text,
                                    'mode': mode,
                                    'motion_data': motion_data,
                                    'frames': len(motion_data),
                                    'joints': 22 if mode == 'pos' else 272
                                }
                            else:
                                response = {
                                    'status': 'error',
                                    'message': '动作生成失败'
                                }
                            
                            await websocket.send(json.dumps(response))
                        
                    elif command == 'ping':
                        await websocket.send(json.dumps({'status': 'pong'}))
                        
                    elif command == 'cancel_generation':
                        # 取消当前生成（如果支持的话）
                        await websocket.send(json.dumps({
                            'status': 'cancelled',
                            'message': '生成已取消'
                        }))
                        
                    else:
                        await websocket.send(json.dumps({
                            'status': 'error',
                            'message': f'未知命令: {command}'
                        }))
                        
                except json.JSONDecodeError:
                    await websocket.send(json.dumps({
                        'status': 'error',
                        'message': '无效的JSON格式'
                    }))
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    await websocket.send(json.dumps({
                        'status': 'error',
                        'message': str(e)
                    }))
                    
        except websockets.exceptions.ConnectionClosed:
            print(f"客户端断开连接: {websocket.remote_address}")
        except Exception as e:
            print(f"连接处理出错: {e}")

    async def start_server(self, host='localhost', port=8765):
        """启动WebSocket服务器"""
        print(f"正在启动WebSocket服务器 {host}:{port}")
        
        # 配置WebSocket服务器参数，优化实时性能和长时间连接
        async with websockets.serve(
            self.handle_client, 
            host, 
            port,
            ping_interval=None,    # 禁用ping，避免长时间生成时超时
            ping_timeout=None,     # 禁用ping超时
            close_timeout=30,      # 关闭超时时间30秒
            max_size=50*1024*1024, # 增加最大消息大小到50MB，支持大型动作数据
            max_queue=64,          # 增加消息队列大小
            compression=None       # 禁用压缩以减少延迟
        ):
            print(f"MotionStreamer WebSocket服务器已启动在 ws://{host}:{port}")
            print("服务器配置:")
            print(f"  - 最大消息大小: 50MB")
            print(f"  - 消息队列大小: 64")
            print(f"  - 压缩: 禁用 (优化实时性)")
            print(f"  - Ping: 禁用 (支持长时间生成)")
            print("等待客户端连接...")
            await asyncio.Future()  # 保持服务器运行

if __name__ == "__main__":
    server = MotionStreamerServer()
    
    # 启动服务器
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        print("\n服务器已停止")
