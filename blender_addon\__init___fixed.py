bl_info = {
    "name": "MotionStreamer MMD Driver",
    "author": "MotionStreamer Team",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > MotionStreamer",
    "description": "通过WebSocket连接MotionStreamer，实现文本到MMD动作的实时生成",
    "category": "Animation",
}

import bpy
import json
import threading
import time
from bpy.props import StringProperty, BoolProperty, EnumProperty, IntProperty, FloatProperty
from bpy.types import Panel, Operator, PropertyGroup

# 可选导入外部库
WEBSOCKETS_AVAILABLE = False
try:
    import asyncio
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    print("警告: websockets库未安装，WebSocket功能将不可用")

# 全局变量
motion_data_cache = []
websocket_connection = None
connection_status = "未连接"

class MotionStreamerProperties(PropertyGroup):
    """MotionStreamer属性组"""
    
    server_host: StringProperty(
        name="服务器地址",
        description="MotionStreamer服务器地址",
        default="localhost"
    )
    
    server_port: IntProperty(
        name="端口",
        description="MotionStreamer服务器端口",
        default=8765,
        min=1,
        max=65535
    )
    
    motion_text: StringProperty(
        name="动作描述",
        description="输入要生成的动作描述文本",
        default="A person is walking forward"
    )
    
    target_armature: StringProperty(
        name="目标骨架",
        description="选择要驱动的MMD模型骨架",
        default=""
    )
    
    auto_play: BoolProperty(
        name="自动播放",
        description="生成动作后自动播放",
        default=True
    )

class MOTIONSTREAMER_OT_connect(Operator):
    """连接到MotionStreamer服务器"""
    bl_idname = "motionstreamer.connect"
    bl_label = "连接服务器"
    bl_description = "连接到MotionStreamer WebSocket服务器"
    
    def execute(self, context):
        if not WEBSOCKETS_AVAILABLE:
            self.report({'ERROR'}, "WebSocket功能不可用，请安装websockets库")
            return {'CANCELLED'}
        
        self.report({'INFO'}, "连接功能已准备就绪")
        return {'FINISHED'}

class MOTIONSTREAMER_OT_generate_motion(Operator):
    """生成动作"""
    bl_idname = "motionstreamer.generate_motion"
    bl_label = "生成动作"
    bl_description = "根据文本描述生成动作"
    
    def execute(self, context):
        props = context.scene.motionstreamer_props
        
        if not props.motion_text.strip():
            self.report({'ERROR'}, "请输入动作描述文本")
            return {'CANCELLED'}
        
        self.report({'INFO'}, f"准备生成动作: {props.motion_text}")
        return {'FINISHED'}

class MOTIONSTREAMER_PT_main_panel(Panel):
    """MotionStreamer主面板"""
    bl_label = "MotionStreamer"
    bl_idname = "MOTIONSTREAMER_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    
    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props
        
        # 连接状态显示
        box = layout.box()
        box.label(text="服务器连接", icon='NETWORK_DRIVE')
        
        row = box.row()
        row.prop(props, "server_host")
        row.prop(props, "server_port")
        
        row = box.row()
        if WEBSOCKETS_AVAILABLE:
            row.operator("motionstreamer.connect", icon='PLAY')
            row.label(text=connection_status, icon='INFO')
        else:
            row.label(text="WebSocket功能不可用", icon='ERROR')

class MOTIONSTREAMER_PT_motion_panel(Panel):
    """动作生成面板"""
    bl_label = "动作生成"
    bl_idname = "MOTIONSTREAMER_PT_motion_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    bl_parent_id = "MOTIONSTREAMER_PT_main_panel"
    
    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props
        
        # 文本输入
        box = layout.box()
        box.label(text="动作描述", icon='TEXT')
        box.prop(props, "motion_text", text="")
        
        # 生成按钮
        row = box.row()
        row.scale_y = 1.5
        row.operator("motionstreamer.generate_motion", icon='PLAY')

class MOTIONSTREAMER_PT_control_panel(Panel):
    """播放控制面板"""
    bl_label = "播放控制"
    bl_idname = "MOTIONSTREAMER_PT_control_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    bl_parent_id = "MOTIONSTREAMER_PT_main_panel"
    
    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props
        
        # 目标设置
        box = layout.box()
        box.label(text="目标设置", icon='ARMATURE_DATA')
        box.prop_search(props, "target_armature", bpy.data, "objects")
        
        # 播放设置
        box.prop(props, "auto_play")

# 注册类列表
classes = [
    MotionStreamerProperties,
    MOTIONSTREAMER_OT_connect,
    MOTIONSTREAMER_OT_generate_motion,
    MOTIONSTREAMER_PT_main_panel,
    MOTIONSTREAMER_PT_motion_panel,
    MOTIONSTREAMER_PT_control_panel,
]

def register():
    """注册插件"""
    try:
        for cls in classes:
            bpy.utils.register_class(cls)
        
        bpy.types.Scene.motionstreamer_props = bpy.props.PointerProperty(type=MotionStreamerProperties)
        print("MotionStreamer插件注册成功")
    except Exception as e:
        print(f"MotionStreamer插件注册失败: {e}")

def unregister():
    """注销插件"""
    try:
        for cls in reversed(classes):
            bpy.utils.unregister_class(cls)
        
        if hasattr(bpy.types.Scene, 'motionstreamer_props'):
            del bpy.types.Scene.motionstreamer_props
        print("MotionStreamer插件注销成功")
    except Exception as e:
        print(f"MotionStreamer插件注销失败: {e}")

if __name__ == "__main__":
    register()