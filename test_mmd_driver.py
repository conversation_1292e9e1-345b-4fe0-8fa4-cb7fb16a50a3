"""
测试MMD驱动器的脚本
用于验证动作数据是否能正确应用到MMD模型
"""

import numpy as np
import json

def generate_test_motion_data():
    """生成测试用的动作数据"""
    # 生成一个简单的走路动作：60帧，22个关节
    frames = 60
    joints = 22
    
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        
        for joint in range(joints):
            # 生成简单的正弦波动作
            t = frame / frames * 2 * np.pi
            
            if joint == 0:  # 根关节 - 前进运动
                x = frame * 0.02  # 向前移动
                y = 0.0
                z = np.sin(t * 2) * 0.05  # 上下摆动
            elif joint in [16, 19]:  # 左右腿 - 交替抬起
                x = 0.0
                y = np.sin(t + (np.pi if joint == 19 else 0)) * 0.1  # 交替抬腿
                z = 0.0
            elif joint in [9, 13]:  # 左右手臂 - 摆臂
                x = np.sin(t + (np.pi if joint == 13 else 0)) * 0.08  # 交替摆臂
                y = 0.0
                z = 0.0
            else:  # 其他关节 - 轻微摆动
                x = np.sin(t) * 0.01
                y = 0.0
                z = 0.0
            
            frame_data.append([x, y, z])
        
        motion_data.append(frame_data)
    
    return motion_data

def save_test_data():
    """保存测试数据到文件"""
    motion_data = generate_test_motion_data()
    
    # 保存为JSON格式
    with open('test_motion_data.json', 'w') as f:
        json.dump(motion_data, f, indent=2)
    
    print(f"测试动作数据已保存，共{len(motion_data)}帧，每帧{len(motion_data[0])}个关节")
    print("数据格式：每帧包含22个关节，每个关节有[x, y, z]坐标")
    
    return motion_data

def print_data_info(motion_data):
    """打印数据信息"""
    if not motion_data:
        print("没有动作数据")
        return
    
    frames = len(motion_data)
    joints = len(motion_data[0])
    
    print(f"动作数据信息：")
    print(f"  帧数: {frames}")
    print(f"  关节数: {joints}")
    print(f"  数据格式: {type(motion_data[0][0])}")
    
    # 打印第一帧的数据示例
    print(f"第一帧数据示例：")
    for i in range(min(5, joints)):
        print(f"  关节{i}: {motion_data[0][i]}")
    
    if joints > 5:
        print(f"  ... (还有{joints-5}个关节)")

if __name__ == "__main__":
    print("生成测试动作数据...")
    motion_data = save_test_data()
    print_data_info(motion_data)
    
    print("\n使用方法：")
    print("1. 在Blender中加载一个MMD模型")
    print("2. 启用MotionStreamer插件")
    print("3. 在插件面板中选择目标骨架")
    print("4. 使用生成的test_motion_data.json文件进行测试")
