name: mgpt
channels:
  - pytorch
  - conda-forge
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_0
  - backcall=0.2.0=pyh9f0ad1d_0
  - blas=1.0=mkl
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2025.1.31=hbcca054_0
  - certifi=2024.8.30=pyhd8ed1ab_0
  - comm=0.2.2=pyhd8ed1ab_0
  - cudatoolkit=10.1.243=h6bb024c_0
  - debugpy=1.4.1=py38h709712a_0
  - entrypoints=0.4=pyhd8ed1ab_0
  - executing=2.1.0=pyhd8ed1ab_0
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.10.4=h5ab3b9f_0
  - gmp=6.2.1=h2531618_2
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2021.3.0=h06a4308_3350
  - ipykernel=6.20.2=pyh210e3f2_0
  - jpeg=9b=h024ee3a_2
  - jupyter_client=7.1.2=pyhd8ed1ab_0
  - jupyter_core=5.7.2=pyh31011fe_1
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgomp=9.3.0=h5101ec6_17
  - libiconv=1.15=h63c8f33_5
  - libidn2=2.3.2=h7f8727e_0
  - libpng=1.6.37=hbc83047_0
  - libsodium=1.0.18=h36c2ea0_1
  - libstdcxx-ng=13.2.0=hc0a3c3a_7
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h85742a9_0
  - libunistring=0.9.10=h27cfd23_0
  - libuv=1.40.0=h7b6447c_0
  - libwebp-base=1.2.0=h27cfd23_0
  - lz4-c=1.9.3=h295c915_1
  - mkl=2021.3.0=h06a4308_520
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.0=py38h42c9631_2
  - mkl_random=1.2.2=py38h51133e4_0
  - ncurses=6.2=he6710b0_1
  - nest-asyncio=1.6.0=pyhd8ed1ab_0
  - nettle=3.7.3=hbbd107a_1
  - ninja=1.10.2=hff7bd54_1
  - olefile=0.46=py_0
  - openh264=2.1.0=hd408876_0
  - openjpeg=2.3.0=h05c96fa_1
  - openssl=1.1.1k=h7f98852_0
  - packaging=24.2=pyhd8ed1ab_2
  - pickleshare=0.7.5=py_1003
  - pillow=8.3.1=py38h2c7a002_0
  - pip=21.0.1=py38h06a4308_0
  - platformdirs=4.3.6=pyhd8ed1ab_0
  - prompt_toolkit=3.0.48=hd8ed1ab_1
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pure_eval=0.2.3=pyhd8ed1ab_0
  - pygments=2.18.0=pyhd8ed1ab_0
  - python=3.8.11=h12debd9_0_cpython
  - python_abi=3.8=5_cp38
  - pyzmq=22.1.0=py38h2035c66_0
  - readline=8.1=h27cfd23_0
  - setuptools=52.0.0=py38h06a4308_0
  - six=1.16.0=pyhd3eb1b0_0
  - sqlite=3.36.0=hc218d9a_0
  - stack_data=0.6.2=pyhd8ed1ab_0
  - tk=8.6.10=hbc83047_0
  - torchaudio=0.8.1=py38
  - torchvision=0.9.1=py38_cu101
  - tornado=6.1=py38h497a2fe_1
  - wheel=0.37.0=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zeromq=4.3.4=h9c3ff4c_0
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.4.9=haebb681_0
  - pip:
      - absl-py==0.13.0
      - accelerate==1.0.1
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.10.11
      - aiosignal==1.3.1
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - async-timeout==5.0.1
      - attrs==24.2.0
      - beautifulsoup4==4.12.3
      - blis==0.7.11
      - cachetools==4.2.2
      - catalogue==2.0.10
      - charset-normalizer==2.0.4
      - chumpy==0.70
      - click==8.1.7
      - clip==1.0
      - cloudpathlib==0.20.0
      - confection==0.1.5
      - cycler==0.10.0
      - cymem==2.0.10
      - decorator==5.0.9
      - diffusers==0.31.0
      - einops==0.8.0
      - ffmpeg-python==0.2.0
      - filelock==3.16.1
      - freetype-py==2.5.1
      - frozenlist==1.5.0
      - fsspec==2024.2.0
      - ftfy==6.1.1
      - future==1.0.0
      - fvcore==0.1.5.post20221221
      - gdown==5.2.0
      - glfw==2.8.0
      - google-auth==2.36.0
      - google-auth-oauthlib==0.4.6
      - grpcio==1.68.0
      - h5py==3.11.0
      - huggingface-hub==0.26.2
      - human-body-prior==*******
      - idna==3.2
      - imageio==2.9.0
      - imageio-ffmpeg==0.5.1
      - importlib-metadata==8.5.0
      - iopath==0.1.10
      - ipdb==0.13.9
      - ipython==7.26.0
      - ipython-genutils==0.2.0
      - jedi==0.18.0
      - jinja2==3.1.3
      - joblib==1.0.1
      - kiwisolver==1.3.1
      - langcodes==3.4.1
      - language-data==1.3.0
      - lightning-utilities==0.11.9
      - marisa-trie==1.2.1
      - markdown==3.3.4
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matplotlib==3.4.3
      - matplotlib-inline==0.1.2
      - mdurl==0.1.2
      - moviepy==*******
      - mpmath==1.3.0
      - multidict==6.1.0
      - murmurhash==1.0.11
      - natsort==8.4.0
      - networkx==3.0
      - numpy==1.22.4
      - nvidia-cublas-cu11==*********
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu11==11.8.87
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu11==11.8.89
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu11==11.8.89
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu11==********
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu11==*********
      - nvidia-cufft-cu12==11.0.2.54
      - nvidia-curand-cu11==10.3.0.86
      - nvidia-curand-cu12==10.3.2.106
      - nvidia-cusolver-cu11==11.4.1.48
      - nvidia-cusolver-cu12==11.4.5.107
      - nvidia-cusparse-cu11==11.7.5.86
      - nvidia-cusparse-cu12==12.1.0.106
      - nvidia-nccl-cu11==2.20.5
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.1.105
      - nvidia-nvtx-cu11==11.8.86
      - nvidia-nvtx-cu12==12.1.105
      - oauthlib==3.1.1
      - omegaconf==2.3.0
      - orjson==3.10.15
      - pandas==1.3.2
      - parso==0.8.2
      - pexpect==4.8.0
      - portalocker==3.0.0
      - preshed==3.0.9
      - prompt-toolkit==3.0.20
      - propcache==0.2.0
      - protobuf==5.28.3
      - psutil==6.1.0
      - pyasn1==0.4.8
      - pyasn1-modules==0.2.8
      - pydantic==2.10.1
      - pydantic-core==2.27.1
      - pydeprecate==0.3.2
      - pygame==2.6.1
      - pyglet==2.1.2
      - pyopengl==3.1.0
      - pyparsing==2.4.7
      - pyrender==0.1.45
      - pysocks==1.7.1
      - python-dateutil==2.8.2
      - pytorch-lightning==1.7.0
      - pytorch3d==0.3.0
      - pytz==2021.1
      - pyyaml==5.4.1
      - regex==2024.11.6
      - requests==2.26.0
      - requests-oauthlib==1.3.0
      - rich==13.9.4
      - rsa==4.7.2
      - safetensors==0.4.5
      - scikit-learn==0.24.2
      - scipy==1.7.1
      - sentence-transformers==3.2.1
      - sentencepiece==0.2.0
      - shapely==2.0.7
      - shellingham==1.5.4
      - sklearn==0.0
      - smart-open==7.0.5
      - smplx==0.1.28
      - soupsieve==2.6
      - spacy==3.7.5
      - spacy-legacy==3.0.12
      - spacy-loggers==1.0.5
      - srsly==2.4.8
      - sympy==1.13.1
      - tabulate==0.9.0
      - tensorboard==2.12.0
      - tensorboard-data-server==0.7.2
      - tensorboard-plugin-wit==1.8.0
      - termcolor==2.4.0
      - thinc==8.2.5
      - threadpoolctl==2.2.0
      - timm==1.0.12
      - tokenizers==0.20.3
      - toml==0.10.2
      - torch==2.4.1+cu118
      - torchgeometry==0.1.2
      - torchmetrics==0.7.0
      - tqdm==4.62.2
      - traitlets==5.0.5
      - transformers==4.46.3
      - triangle==20250106
      - trimesh==4.6.2
      - triton==3.0.0
      - typer==0.13.1
      - typing-extensions==4.12.2
      - urllib3==1.26.6
      - wasabi==1.1.3
      - wcwidth==0.2.5
      - weasel==0.4.1
      - werkzeug==2.0.1
      - wrapt==1.17.0
      - yacs==0.1.8
      - yarl==1.15.2
      - zipp==3.20.2
prefix: /root/miniconda3/envs/mgpt