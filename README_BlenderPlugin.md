# MotionStreamer Blender插件使用指南

这个Blender插件可以通过WebSocket连接到MotionStreamer项目，实现文本到MMD动作的实时生成和播放。

## 功能特性

- 🌐 **WebSocket通信**: 与MotionStreamer服务器实时通信
- 📝 **文本输入**: 输入自然语言描述生成对应动作
- 🎭 **MMD支持**: 直接驱动Blender中的MMD模型
- 🎬 **实时播放**: 生成的动作可立即在Blender中播放
- ⚙️ **灵活配置**: 支持位置和旋转两种动作模式

## 安装步骤

### 1. 环境准备

首先确保你的系统已安装：
- Python 3.8+
- Blender 3.0+
- CUDA支持的GPU（推荐，CPU也可运行但较慢）

### 2. 安装MotionStreamer依赖

在MotionStreamer项目根目录运行：

```bash
# 检查并安装依赖
python install_requirements.py

# 或手动安装
pip install websockets torch numpy sentence-transformers transformers safetensors
```

### 3. 安装Blender插件

1. 打开Blender
2. 进入 `Edit` > `Preferences` > `Add-ons`
3. 点击 `Install...` 按钮
4. 选择 `blender_addon` 文件夹或将其压缩为zip文件后选择
5. 在插件列表中找到 "MotionStreamer MMD Driver" 并启用

## 使用方法

### 1. 启动WebSocket服务器

在MotionStreamer项目根目录运行：

```bash
python websocket_server.py
```

服务器启动后会显示：
```
MotionStreamer WebSocket服务器已启动在 ws://localhost:8765
等待客户端连接...
```

### 2. 在Blender中使用插件

1. **打开插件面板**
   - 在3D视图中按 `N` 键打开侧边栏
   - 找到 "MotionStreamer" 标签页

2. **连接服务器**
   - 确认服务器地址和端口（默认：localhost:8765）
   - 点击 "连接服务器" 按钮
   - 连接成功后状态会显示 "已连接"

3. **准备MMD模型**
   - 导入你的MMD模型到Blender
   - 确保模型有标准的MMD骨骼结构
   - 在插件的 "目标设置" 中选择骨架对象

4. **生成动作**
   - 在 "动作描述" 框中输入文本，例如：
     - "A person is walking forward"
     - "Someone is dancing happily"
     - "A man is running and jumping"
   - 选择动作模式（位置模式推荐）
   - 点击 "生成动作" 按钮

5. **播放动画**
   - 动作生成完成后会自动应用到选定的骨架
   - 使用播放控制按钮控制动画播放
   - 可以调整播放速度和循环设置

## 插件界面说明

### 服务器连接面板
- **服务器地址**: WebSocket服务器的IP地址
- **端口**: WebSocket服务器的端口号
- **连接/断开按钮**: 管理服务器连接
- **连接状态**: 显示当前连接状态

### 动作生成面板
- **动作描述**: 输入要生成的动作文本描述
- **动作模式**: 
  - `位置模式`: 使用关节位置数据（推荐）
  - `旋转模式`: 使用272维旋转数据
- **生成动作按钮**: 发送请求生成动作
- **动作信息**: 显示生成的动作帧数和关节数

### 播放控制面板
- **目标骨架**: 选择要驱动的MMD模型骨架
- **自动播放**: 生成动作后自动开始播放
- **播放速度**: 调整动画播放速度倍数
- **循环播放**: 是否循环播放动画
- **播放/停止按钮**: 手动控制动画播放

## MMD骨骼映射

插件会自动将HumanML3D的22个关节映射到MMD骨骼：

| HumanML3D关节 | MMD骨骼名称 | 说明 |
|---------------|-------------|------|
| pelvis | センター | 骨盆/中心 |
| spine1 | 上半身 | 上半身 |
| spine2 | 上半身2 | 上半身2 |
| neck | 首 | 脖子 |
| head | 頭 | 头部 |
| left_shoulder | 左腕 | 左肩膀 |
| left_elbow | 左ひじ | 左肘 |
| left_wrist | 左手首 | 左手腕 |
| right_shoulder | 右腕 | 右肩膀 |
| right_elbow | 右ひじ | 右肘 |
| right_wrist | 右手首 | 右手腕 |
| left_hip | 左足 | 左大腿 |
| left_knee | 左ひざ | 左膝盖 |
| left_ankle | 左足首 | 左脚踝 |
| right_hip | 右足 | 右大腿 |
| right_knee | 右ひざ | 右膝盖 |
| right_ankle | 右足首 | 右脚踝 |

## 故障排除

### 连接问题
- **无法连接服务器**: 
  - 确认WebSocket服务器已启动
  - 检查防火墙设置
  - 确认IP地址和端口正确

### 动作生成问题
- **生成失败**: 
  - 检查服务器控制台的错误信息
  - 确认模型文件完整
  - 尝试简化文本描述

### 动画播放问题
- **动作不自然**: 
  - 尝试切换到位置模式
  - 调整播放速度
  - 检查MMD模型的骨骼结构

### 性能问题
- **生成速度慢**: 
  - 确认使用GPU加速
  - 关闭其他占用GPU的程序
  - 考虑使用更短的文本描述

## 技术细节

### WebSocket协议
插件使用JSON格式与服务器通信：

```json
// 请求格式
{
    "command": "generate_motion",
    "text": "A person is walking",
    "mode": "pos"
}

// 响应格式
{
    "status": "success",
    "text": "A person is walking",
    "mode": "pos",
    "motion_data": [...],
    "frames": 120,
    "joints": 22
}
```

### 数据格式
- **位置模式**: 返回 `[frames, 22, 3]` 的关节位置数据
- **旋转模式**: 返回 `[frames, 272]` 的完整动作表示

## 开发和扩展

如果你想修改或扩展插件功能：

1. **修改骨骼映射**: 编辑 `apply_position_data()` 函数中的 `joint_mapping` 字典
2. **添加新功能**: 在相应的Operator类中添加新方法
3. **自定义UI**: 修改Panel类的 `draw()` 方法
4. **扩展通信协议**: 在服务器和插件中同步添加新的命令类型

## 许可证

本插件遵循与MotionStreamer项目相同的许可证。

## 支持

如遇问题，请：
1. 检查控制台输出的错误信息
2. 确认环境配置正确
3. 参考故障排除部分
4. 在项目仓库中提交Issue
