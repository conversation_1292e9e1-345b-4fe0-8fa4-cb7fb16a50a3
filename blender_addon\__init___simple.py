bl_info = {
    "name": "MotionStreamer Test",
    "author": "Test",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > Test",
    "description": "Simple test addon",
    "category": "Animation",
}

import bpy
from bpy.types import Panel, Operator

print("MotionStreamer: WebSocket库加载--------------------")
import bpy
import json
import threading
import time
from bpy.props import StringProperty, BoolProperty, EnumProperty, IntProperty, FloatProperty
from bpy.types import Panel, Operator, PropertyGroup

# 可选导入外部库
WEBSOCKETS_AVAILABLE = False
try:
    import asyncio
    import websockets
    WEBSOCKETS_AVAILABLE = True
    print("MotionStreamer: WebSocket库加载成功")
except ImportError:
    print("MotionStreamer: 警告 - websockets库未安装，WebSocket功能将不可用")

class TEST_OT_hello(Operator):
    """Test operator"""
    bl_idname = "test.hello"
    bl_label = "Hello World"
    bl_description = "Test button"
    
    def execute(self, context):
        self.report({'INFO'}, "Hello from MotionStreamer!")
        return {'FINISHED'}

class TEST_PT_panel(Panel):
    """Test panel"""
    bl_label = "MotionStreamer Test"
    bl_idname = "TEST_PT_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Test"
    
    def draw(self, context):
        layout = self.layout
        layout.operator("test.hello")

classes = [
    TEST_OT_hello,
    TEST_PT_panel,
]

def register():
    print("MotionStreamer: 开始注册插件...")
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    print("MotionStreamer: 开始注销插件...")
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()