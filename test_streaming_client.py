import asyncio
import websockets
import json
import time

# 添加性能监控变量
frame_count = 0
last_frame_time = 0
frame_rate_samples = []

async def test_streaming_client():
    global frame_count, last_frame_time, frame_rate_samples
    
    # 重置性能监控变量
    frame_count = 0
    last_frame_time = 0
    frame_rate_samples = []
    
    uri = "ws://localhost:8765"
    print(f"连接到服务器: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("WebSocket连接成功!")
            
            # 发送流式生成请求
            request = {
                "command": "generate_motion",
                "text": "A person is walking forward",
                "mode": "pos",
                "streaming": True
            }
            
            await websocket.send(json.dumps(request))
            print(f"发送请求: {request['text']}")
            
            start_time = time.time()
            
            # 接收流式响应
            async for message in websocket:
                try:
                    data = json.loads(message)
                    status = data.get('status')
                    
                    if status == 'streaming_start':
                        generation_id = data.get('generation_id')
                        print(f"开始生成 (ID: {generation_id}): {data.get('message')}")
                        
                    elif status == 'streaming_frames':
                        current_token = data.get('current_token', 0)
                        progress = data.get('progress_percent', 0)
                        new_frames = data.get('new_frames', [])
                        total_frames = data.get('total_frames', 0)
                        first_frame_time = data.get('first_frame_time')
                        elapsed = time.time() - start_time
                        
                        # 性能监控：计算帧率
                        current_time = time.time()
                        if last_frame_time > 0:
                            frame_interval = current_time - last_frame_time
                            if frame_interval > 0:
                                frame_rate = len(new_frames) / frame_interval
                                frame_rate_samples.append(frame_rate)
                                # 保持最近10个样本
                                if len(frame_rate_samples) > 10:
                                    frame_rate_samples.pop(0)
                                avg_frame_rate = sum(frame_rate_samples) / len(frame_rate_samples)
                        else:
                            avg_frame_rate = 0
                        
                        last_frame_time = current_time
                        frame_count += len(new_frames)
                        
                        # 显示首帧耗时信息和性能信息
                        first_frame_info = f", 首帧耗时: {first_frame_time:.3f}s" if first_frame_time else ""
                        perf_info = f", 平均帧率: {avg_frame_rate:.1f}帧/s" if avg_frame_rate > 0 else ""
                        
                        print(f"接收到新帧数据: +{len(new_frames)} 帧, 总计: {total_frames} 帧, token: {current_token}, 进度: {progress:.1f}%, 耗时: {elapsed:.2f}s{first_frame_info}{perf_info}")
                        
                    elif status == 'streaming_complete':
                        total_time = data.get('total_time', 0)
                        total_frames = data.get('total_frames', 0)
                        joints = data.get('joints', 0)
                        first_frame_time = data.get('first_frame_time')
                        
                        # 计算总体性能指标
                        overall_frame_rate = total_frames / total_time if total_time > 0 else 0
                        
                        print(f"生成完成! 总帧数: {total_frames}, 关节数: {joints}, 总耗时: {total_time:.2f}s")
                        print(f"  - 总耗时: {total_time:.2f}秒")
                        print(f"  - 动作帧数: {total_frames}")
                        print(f"  - 关节数: {joints}")
                        print(f"  - 总体帧率: {overall_frame_rate:.1f}帧/s")
                        if first_frame_time:
                            print(f"  - 首帧耗时: {first_frame_time:.3f}秒")
                        break
                        
                    elif status == 'streaming_error':
                        print(f"生成错误: {data.get('message')}")
                        break
                        
                    elif status == 'error':
                        print(f"服务器错误: {data.get('message')}")
                        break
                        
                except json.JSONDecodeError:
                    print(f"无法解析响应: {message}")
                except Exception as e:
                    print(f"处理响应时出错: {e}")
            
            # 测试传统生成模式对比
            print("\n" + "="*50)
            print("测试传统生成模式...")
            
            request_traditional = {
                 'command': 'generate_motion',
                 'text': "A person is walking forward",
                 'mode': 'pos',
                 'streaming': False  # 禁用流式生成
             }
            
            await websocket.send(json.dumps(request_traditional))
            start_time_traditional = time.time()
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    status = data.get('status')
                    
                    if status == 'generating':
                        print(f"传统模式: {data.get('message')}")
                        
                    elif status == 'success':
                        elapsed = time.time() - start_time_traditional
                        frames = data.get('frames', 0)
                        joints = data.get('joints', 0)
                        print(f"传统模式完成!")
                        print(f"  - 总耗时: {elapsed:.2f}秒")
                        print(f"  - 动作帧数: {frames}")
                        print(f"  - 关节数: {joints}")
                        break
                        
                    elif status == 'error':
                        print(f"传统模式错误: {data.get('message')}")
                        break
                        
                except json.JSONDecodeError:
                    print(f"无法解析响应: {message}")
                except Exception as e:
                    print(f"处理响应时出错: {e}")
                    
    except ConnectionRefusedError:
        print("无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"客户端错误: {e}")

async def test_multiple_requests():
    """测试多个并发请求"""
    uri = "ws://localhost:8765"
    
    async def single_request(text, client_id):
        try:
            async with websockets.connect(uri) as websocket:
                request = {
                    'command': 'generate_motion',
                    'text': text,
                    'mode': 'pos',
                    'streaming': True
                }
                
                print(f"客户端 {client_id}: 发送请求 '{text}'")
                await websocket.send(json.dumps(request))
                
                start_time = time.time()
                
                async for message in websocket:
                    data = json.loads(message)
                    status = data.get('status')
                    
                    if status == 'streaming_start':
                        print(f"客户端 {client_id}: 开始生成")
                        
                    elif status == 'streaming_progress':
                        progress = data.get('progress_percent', 0)
                        if progress % 20 == 0:  # 每20%打印一次
                            print(f"客户端 {client_id}: 进度 {progress:.0f}%")
                        
                    elif status == 'streaming_complete':
                        elapsed = time.time() - start_time
                        print(f"客户端 {client_id}: 完成，耗时 {elapsed:.2f}秒")
                        break
                        
                    elif status in ['streaming_error', 'error']:
                        print(f"客户端 {client_id}: 错误 - {data.get('message')}")
                        break
                        
        except Exception as e:
            print(f"客户端 {client_id} 错误: {e}")
    
    # 并发测试
    tasks = [
        single_request("a person is walking", 1),
        single_request("a person is running", 2),
        single_request("a person is jumping", 3)
    ]
    
    print("开始并发测试...")
    await asyncio.gather(*tasks)
    print("并发测试完成")

if __name__ == "__main__":
    print("MotionStreamer 流式生成测试客户端")
    print("=" * 50)
    
    # 运行基本测试
    asyncio.run(test_streaming_client())
    
    # 运行并发测试
    print("\n" + "=" * 50)
    print("开始并发测试...")
    asyncio.run(test_multiple_requests())