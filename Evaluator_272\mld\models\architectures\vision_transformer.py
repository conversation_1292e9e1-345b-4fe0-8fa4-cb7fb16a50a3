""" 
This script is borrowed from https://github.com/rwightman/pytorch-image-models.
Adhere to their licence to use this script

We hacked it a little bit to make it happy in our framework.
"""
import torch
import torch.nn as nn
import torch.utils.model_zoo as model_zoo
import math
import warnings
import random
import numpy as np
import joblib

from collections import OrderedDict
from functools import partial
from itertools import repeat
# from torch._six import container_abcs

from mld.utils.maed_utils import DropPath, determine_output_feature_dim, load_state_dict
from mld.models.architectures.hrnet import get_hrnet
from mld.models.architectures.resnetv2 import ResNetV2
from .ghost_nas_network import get_ghostnas
from .ghost_nas_network_tiny import get_ghostnas as get_ghostnas_tiny
# from torchvision.models.utils import load_state_dict_from_url

import torch
import torch.nn as nn
import torch.utils.model_zoo as model_zoo
import math
import warnings
import random
import numpy as np
import joblib

from collections import OrderedDict
from functools import partial
from itertools import repeat
# from torch._six import container_abcs

model_urls = {
    'vit_tiny_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p16_224-80ecf9dd.pth',
    'vit_small_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-weights/vit_small_p16_224-15ec54c9.pth',
    'vit_base_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p16_224-80ecf9dd.pth',
    'vit_base_patch16_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p16_384-83fb41ba.pth',
    'vit_base_patch32_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p32_384-830016f5.pth',
    'vit_large_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_large_p16_224-4ee7a4dc.pth',
    'vit_large_patch16_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_large_p16_384-b3be5167.pth',
    'vit_large_patch32_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_large_p32_384-9b920ba8.pth',
    'vit_base_resnet50_224_in21k':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_resnet50_224_in21k-6f7c7740.pth',
}

model_urls = {
    'vit_tiny_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p16_224-80ecf9dd.pth',
    'vit_small_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-weights/vit_small_p16_224-15ec54c9.pth',
    'vit_base_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p16_224-80ecf9dd.pth',
    'vit_base_patch16_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p16_384-83fb41ba.pth',
    'vit_base_patch32_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_p32_384-830016f5.pth',
    'vit_large_patch16_224':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_large_p16_224-4ee7a4dc.pth',
    'vit_large_patch16_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_large_p16_384-b3be5167.pth',
    'vit_large_patch32_384':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_large_p32_384-9b920ba8.pth',
    'vit_base_resnet50_224_in21k':
    'https://github.com/rwightman/pytorch-image-models/releases/download/v0.1-vitjx/jx_vit_base_resnet50_224_in21k-6f7c7740.pth',
}


def _no_grad_trunc_normal_(tensor, mean, std, a, b):
    # Cut & paste from PyTorch official master until it's in a few official releases - RW
    # Method based on https://people.sc.fsu.edu/~jburkardt/presentations/truncated_normal.pdf
    def norm_cdf(x):
        # Computes standard normal cumulative distribution function
        return (1. + math.erf(x / math.sqrt(2.))) / 2.

    if (mean < a - 2 * std) or (mean > b + 2 * std):
        warnings.warn(
            "mean is more than 2 std from [a, b] in nn.init.trunc_normal_. "
            "The distribution of values may be incorrect.",
            stacklevel=2)

    with torch.no_grad():
        # Values are generated by using a truncated uniform distribution and
        # then using the inverse CDF for the normal distribution.
        # Get upper and lower cdf values
        l = norm_cdf((a - mean) / std)
        u = norm_cdf((b - mean) / std)

        # Uniformly fill tensor with values from [l, u], then translate to
        # [2l-1, 2u-1].
        tensor.uniform_(2 * l - 1, 2 * u - 1)

        # Use inverse cdf transform for normal distribution to get truncated
        # standard normal
        tensor.erfinv_()

        # Transform to proper mean, std
        tensor.mul_(std * math.sqrt(2.))
        tensor.add_(mean)

        # Clamp to ensure it's in the proper range
        tensor.clamp_(min=a, max=b)
        return tensor


def trunc_normal_(tensor, mean=0., std=1., a=-2., b=2.):
    # type: (Tensor, float, float, float, float) -> Tensor
    r"""Fills the input Tensor with values drawn from a truncated
    normal distribution. The values are effectively drawn from the
    normal distribution :math:`\mathcal{N}(\text{mean}, \text{std}^2)`
    with values outside :math:`[a, b]` redrawn until they are within
    the bounds. The method used for generating the random values works
    best when :math:`a \leq \text{mean} \leq b`.
    Args:
        tensor: an n-dimensional `torch.Tensor`
        mean: the mean of the normal distribution
        std: the standard deviation of the normal distribution
        a: the minimum cutoff value
        b: the maximum cutoff value
    Examples:
        >>> w = torch.empty(3, 5)
        >>> nn.init.trunc_normal_(w)
    """
    return _no_grad_trunc_normal_(tensor, mean, std, a, b)


class Mlp(nn.Module):

    def __init__(self,
                 in_features,
                 hidden_features=None,
                 out_features=None,
                 act_layer=nn.GELU,
                 drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class Attention(nn.Module):

    def __init__(self,
                 dim,
                 num_heads=8,
                 qkv_bias=False,
                 qk_scale=None,
                 attn_drop=0.,
                 proj_drop=0.,
                 st_mode='vanilla'):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        # NOTE scale factor was wrong in my original version, can set manually to be compat with prev weights
        self.scale = qk_scale or head_dim**-0.5

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.mode = st_mode
        if self.mode == 'parallel':
            self.ts_attn = nn.Linear(dim * 2, dim * 2)
            self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        else:
            self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.proj_drop = nn.Dropout(proj_drop)

        self.attn_count_s = None
        self.attn_count_t = None

    def forward(self, x, seqlen=1):
        B, N, C = x.shape

        if self.mode == 'series':
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads,
                                      C // self.num_heads).permute(
                                          2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[
                2]  # make torchscript happy (cannot use tensor as tuple)
            x = self.forward_spatial(q, k, v)
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads,
                                      C // self.num_heads).permute(
                                          2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[
                2]  # make torchscript happy (cannot use tensor as tuple)
            x = self.forward_temporal(q, k, v, seqlen=seqlen)
        elif self.mode == 'parallel':
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads,
                                      C // self.num_heads).permute(
                                          2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[
                2]  # make torchscript happy (cannot use tensor as tuple)
            x_t = self.forward_temporal(q, k, v, seqlen=seqlen)
            x_s = self.forward_spatial(q, k, v)

            alpha = torch.cat([x_s, x_t], dim=-1)
            alpha = alpha.mean(dim=1, keepdim=True)
            alpha = self.ts_attn(alpha).reshape(B, 1, C, 2)
            alpha = alpha.softmax(dim=-1)
            #self.count_attn(alpha)

            x = x_t * alpha[:, :, :, 1] + x_s * alpha[:, :, :, 0]
            #x = (x_t + x_s) / 2
        elif self.mode == 'coupling':
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads,
                                      C // self.num_heads).permute(
                                          2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[
                2]  # make torchscript happy (cannot use tensor as tuple)
            x = self.forward_coupling(q, k, v, seqlen=seqlen)
        elif self.mode == 'vanilla':
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads,
                                      C // self.num_heads).permute(
                                          2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[
                2]  # make torchscript happy (cannot use tensor as tuple)
            x = self.forward_spatial(q, k, v)
        elif self.mode == 'temporal':
            x = x.mean(dim=1, keepdim=True)
            N = 1
            qkv = self.qkv(x).reshape(B, N, 3, self.num_heads,
                                      C // self.num_heads).permute(
                                          2, 0, 3, 1, 4)
            q, k, v = qkv[0], qkv[1], qkv[
                2]  # make torchscript happy (cannot use tensor as tuple)
            x = self.forward_temporal(q, k, v, seqlen=seqlen)
        else:
            raise NotImplementedError(self.mode)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

    def reshape_T(self, x, seqlen=1, inverse=False):
        if not inverse:
            N, C = x.shape[-2:]
            x = x.reshape(-1, seqlen, self.num_heads, N, C).transpose(1, 2)
            x = x.reshape(-1, self.num_heads, seqlen * N, C)  #(B, H, TN, c)
        else:
            TN, C = x.shape[-2:]
            x = x.reshape(-1, self.num_heads, seqlen, TN // seqlen,
                          C).transpose(1, 2)
            x = x.reshape(-1, self.num_heads, TN // seqlen, C)  #(BT, H, N, C)
        return x

    def forward_coupling(self, q, k, v, seqlen=8):
        BT, _, N, C = q.shape
        q = self.reshape_T(q, seqlen)
        k = self.reshape_T(k, seqlen)
        v = self.reshape_T(v, seqlen)

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = attn @ v
        x = self.reshape_T(x, seqlen, inverse=True)
        x = x.transpose(1, 2).reshape(BT, N, C * self.num_heads)
        return x

    def forward_spatial(self, q, k, v):
        B, _, N, C = q.shape
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = attn @ v
        x = x.transpose(1, 2).reshape(B, N, C * self.num_heads)
        return x

    def forward_temporal(self, q, k, v, seqlen=8):
        B, _, N, C = q.shape
        qt = q.reshape(-1, seqlen, self.num_heads, N,
                       C).permute(0, 2, 3, 1, 4)  #(B, H, N, T, C)
        kt = k.reshape(-1, seqlen, self.num_heads, N,
                       C).permute(0, 2, 3, 1, 4)  #(B, H, N, T, C)
        vt = v.reshape(-1, seqlen, self.num_heads, N,
                       C).permute(0, 2, 3, 1, 4)  #(B, H, N, T, C)

        attn = (qt @ kt.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = attn @ vt  #(B, H, N, T, C)
        x = x.permute(0, 3, 2, 1, 4).reshape(B, N, C * self.num_heads)
        return x

    def count_attn(self, attn):
        attn = attn.detach().cpu().numpy()
        attn = attn.mean(axis=1)
        attn_t = attn[:, :, 1].mean(axis=1)
        attn_s = attn[:, :, 0].mean(axis=1)
        if self.attn_count_s is None:
            self.attn_count_s = attn_s
            self.attn_count_t = attn_t
        else:
            self.attn_count_s = np.concatenate([self.attn_count_s, attn_s],
                                               axis=0)
            self.attn_count_t = np.concatenate([self.attn_count_t, attn_t],
                                               axis=0)


class Block(nn.Module):

    def __init__(self,
                 dim,
                 num_heads,
                 mlp_ratio=4.,
                 qkv_bias=False,
                 qk_scale=None,
                 drop=0.,
                 attn_drop=0.,
                 drop_path=0.,
                 act_layer=nn.GELU,
                 norm_layer=nn.LayerNorm,
                 st_mode='vanilla'):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.attn = Attention(dim,
                              num_heads=num_heads,
                              qkv_bias=qkv_bias,
                              qk_scale=qk_scale,
                              attn_drop=attn_drop,
                              proj_drop=drop,
                              st_mode=st_mode)
        # NOTE: drop path for stochastic depth, we shall see if this is better than dropout here
        self.drop_path = DropPath(
            drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim,
                       hidden_features=mlp_hidden_dim,
                       act_layer=act_layer,
                       drop=drop)

    def forward(self, x, seqlen=1):
        x = x + self.drop_path(self.attn(self.norm1(x), seqlen))
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        return x


class PatchEmbed(nn.Module):
    """ Image to Patch Embedding
    """

    def __init__(self, img_size=224, patch_size=16, in_chans=3, embed_dim=768):
        super().__init__()
        img_size = tuple(repeat(img_size, 2))
        patch_size = tuple(repeat(patch_size, 2))
        num_patches = (img_size[1] // patch_size[1]) * (img_size[0] //
                                                        patch_size[0])
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = num_patches

        self.proj = nn.Conv2d(in_chans,
                              embed_dim,
                              kernel_size=patch_size,
                              stride=patch_size)

    def forward(self, x):
        B, C, H, W = x.shape
        # FIXME look at relaxing size constraints
        assert H == self.img_size[0] and W == self.img_size[1], \
            f"Input image size ({H}*{W}) doesn't match model ({self.img_size[0]}*{self.img_size[1]})."
        x = self.proj(x).flatten(2).transpose(1, 2)
        return x


class HybridEmbed(nn.Module):
    """ CNN Feature Map Embedding
    Extract feature map from CNN, flatten, project to embedding dim.
    """

    def __init__(self,
                 backbone,
                 img_size=224,
                 feature_size=None,
                 in_chans=3,
                 embed_dim=768):
        super().__init__()
        assert isinstance(backbone, nn.Module)
        img_size = tuple(repeat(img_size, 2))
        self.img_size = img_size
        self.backbone = backbone
        if feature_size is None:
            feature_size, feature_dim = determine_output_feature_dim(
                inp_size=(1, in_chans, img_size[0], img_size[1]),
                model=self.backbone)
        else:
            feature_size = tuple(repeat(feature_size, n))
            feature_dim = self.backbone.feature_info.channels()[-1]
        self.num_patches = feature_size[0] * feature_size[1]
        self.proj = nn.Conv2d(feature_dim, embed_dim, 1)

    def forward(self, x):
        x = self.backbone(x)
        if isinstance(x, (list, tuple)):
            x = x[
                -1]  # last feature if backbone outputs list/tuple of features
        x = self.proj(x).flatten(2).transpose(1, 2)
        return x


class VisionTransformer(nn.Module):
    """ Vision Transformer with support for patch or hybrid CNN input stage
    """

    def __init__(self,
                 img_size=224,
                 patch_size=16,
                 in_chans=3,
                 num_classes=1000,
                 embed_dim=768,
                 depth=12,
                 num_heads=12,
                 mlp_ratio=4.,
                 qkv_bias=False,
                 qk_scale=None,
                 representation_size=None,
                 drop_rate=0.,
                 attn_drop_rate=0.,
                 drop_path_rate=0.,
                 hybrid_backbone=None,
                 norm_layer=nn.LayerNorm,
                 st_mode='vanilla'):
        super().__init__()
        self.num_classes = num_classes
        self.num_features = self.embed_dim = embed_dim  # num_features for consistency with other models

        if hybrid_backbone is not None:
            self.patch_embed = HybridEmbed(hybrid_backbone,
                                           img_size=img_size,
                                           in_chans=in_chans,
                                           embed_dim=embed_dim)
        else:
            self.patch_embed = PatchEmbed(img_size=img_size,
                                          patch_size=patch_size,
                                          in_chans=in_chans,
                                          embed_dim=embed_dim)
        num_patches = self.patch_embed.num_patches

        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_embed = nn.Parameter(
            torch.zeros(1, num_patches + 1, embed_dim))
        self.pos_drop = nn.Dropout(p=drop_rate)

        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)
               ]  # stochastic depth decay rule
        self.blocks = nn.ModuleList([
            Block(dim=embed_dim,
                  num_heads=num_heads,
                  mlp_ratio=mlp_ratio,
                  qkv_bias=qkv_bias,
                  qk_scale=qk_scale,
                  drop=drop_rate,
                  attn_drop=attn_drop_rate,
                  drop_path=dpr[i],
                  norm_layer=norm_layer,
                  st_mode=st_mode) for i in range(depth)
        ])
        self.norm = norm_layer(embed_dim)
        self.st_mode = st_mode

        # Representation layer
        if representation_size:
            self.num_features = representation_size
            self.pre_logits = nn.Sequential(
                OrderedDict([('fc', nn.Linear(embed_dim, representation_size)),
                             ('act', nn.Tanh())]))
        else:
            self.pre_logits = nn.Identity()

        # Classifier head
        self.head = nn.Linear(
            embed_dim, num_classes) if num_classes > 0 else nn.Identity()

        trunc_normal_(self.pos_embed, std=.02)
        trunc_normal_(self.cls_token, std=.02)

        if st_mode in ['coupling', 'parallel', 'series']:
            self.temp_embed = nn.Parameter(torch.zeros(1, 16, 1, embed_dim))
            trunc_normal_(self.temp_embed, std=.02)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    @torch.jit.ignore
    def no_weight_decay(self):
        return {'pos_embed', 'cls_token'}

    def get_classifier(self):
        return self.head

    def reset_classifier(self, num_classes, global_pool=''):
        self.num_classes = num_classes
        self.head = nn.Linear(
            self.embed_dim, num_classes) if num_classes > 0 else nn.Identity()

    def forward_features(self, x, seqlen=1):
        B = x.shape[0]
        x = self.patch_embed(x)

        cls_tokens = self.cls_token.expand(
            B, -1, -1)  # stole cls_tokens impl from Phil Wang, thanks
        x = torch.cat((cls_tokens, x), dim=1)
        x = x + self.pos_embed

        if self.st_mode in ['coupling', 'parallel', 'series']:
            _, N, C = x.shape
            x = x.reshape(-1, seqlen, N, C) + self.temp_embed[:, :seqlen, :, :]
            x = x.reshape(B, N, C)

        x = self.pos_drop(x)

        for blk in self.blocks:
            x = blk(x, seqlen)

        x = self.norm(x)[:, 0]
        x = self.pre_logits(x)
        return x

    def forward(self, x, seqlen=1):
        x = self.forward_features(x, seqlen)
        x = self.head(x)
        return x


def _conv_filter(state_dict, patch_size=16):
    """ convert patch embedding weight from manual patchify + linear proj to conv"""
    out_dict = {}
    for k, v in state_dict.items():
        if 'patch_embed.proj.weight' in k and len(v.shape) < 4:
            v = v.reshape((v.shape[0], 3, patch_size, patch_size))
        out_dict[k] = v
    return out_dict


def vit_small_patch16_224(pretrained=False, strict=True, **kwargs):
    if pretrained:
        # NOTE my scale was wrong for original weights, leaving this here until I have better ones for this model
        kwargs.setdefault('qk_scale', 768**-0.5)
    model = VisionTransformer(patch_size=16,
                              embed_dim=768,
                              depth=8,
                              num_heads=8,
                              mlp_ratio=3.,
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_small_patch16_224'],
                                        progress=False,
                                        map_location='cpu')
        state_dict = _conv_filter(state_dict)
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_base_patch16_224(pretrained=False, strict=True, **kwargs):
    model = VisionTransformer(patch_size=16,
                              embed_dim=768,
                              depth=12,
                              num_heads=12,
                              mlp_ratio=4,
                              qkv_bias=True,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_base_patch16_224'],
                                        progress=False,
                                        map_location='cpu')
        state_dict = _conv_filter(state_dict)
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_base_patch16_384(pretrained=False, strict=True, **kwargs):
    model = VisionTransformer(img_size=384,
                              patch_size=16,
                              embed_dim=768,
                              depth=12,
                              num_heads=12,
                              mlp_ratio=4,
                              qkv_bias=True,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_base_patch16_384'],
                                        progress=False,
                                        map_location='cpu')
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_base_patch32_384(pretrained=False, strict=True, **kwargs):
    model = VisionTransformer(img_size=384,
                              patch_size=32,
                              embed_dim=768,
                              depth=12,
                              num_heads=12,
                              mlp_ratio=4,
                              qkv_bias=True,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_base_patch32_384'],
                                        progress=False,
                                        map_location='cpu')
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_large_patch16_224(pretrained=False, strict=True, **kwargs):
    model = VisionTransformer(patch_size=16,
                              embed_dim=1024,
                              depth=24,
                              num_heads=16,
                              mlp_ratio=4,
                              qkv_bias=True,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_large_patch16_224'],
                                        progress=False,
                                        map_location='cpu')
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_large_patch16_384(pretrained=False, strict=True, **kwargs):
    model = VisionTransformer(img_size=384,
                              patch_size=16,
                              embed_dim=1024,
                              depth=24,
                              num_heads=16,
                              mlp_ratio=4,
                              qkv_bias=True,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_large_patch16_384'],
                                        progress=False,
                                        map_location='cpu')
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_large_patch32_384(pretrained=False, strict=True, **kwargs):
    model = VisionTransformer(img_size=384,
                              patch_size=32,
                              embed_dim=1024,
                              depth=24,
                              num_heads=16,
                              mlp_ratio=4,
                              qkv_bias=True,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(model_urls['vit_large_patch32_384'],
                                        progress=False,
                                        map_location='cpu')
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_huge_patch16_224(pretrained=False, **kwargs):
    model = VisionTransformer(patch_size=16,
                              embed_dim=1280,
                              depth=32,
                              num_heads=16,
                              mlp_ratio=4,
                              **kwargs)
    model.default_cfg = default_cfgs['vit_huge_patch16_224']
    return model


def vit_huge_patch32_384(pretrained=False, **kwargs):
    model = VisionTransformer(img_size=384,
                              patch_size=32,
                              embed_dim=1280,
                              depth=32,
                              num_heads=16,
                              mlp_ratio=4,
                              **kwargs)
    model.default_cfg = default_cfgs['vit_huge_patch32_384']
    return model


def vit_base_resnet50_224_in21k(pretrained=False, strict=True, **kwargs):
    """ R50+ViT-B/16 hybrid model from original paper (https://arxiv.org/abs/2010.11929).
    ImageNet-21k weights @ 224x224, source https://github.com/google-research/vision_transformer.
    """
    # create a ResNetV2 w/o pre-activation, that uses StdConv and GroupNorm and has 3 stages, no head
    backbone = ResNetV2(layers=(3, 4, 9),
                        num_classes=0,
                        global_pool='',
                        in_chans=kwargs.get('in_chans', 3),
                        preact=False,
                        stem_type='same')
    model = VisionTransformer(patch_size=16,
                              embed_dim=768,
                              depth=12,
                              num_heads=12,
                              hybrid_backbone=backbone,
                              mlp_ratio=4,
                              qkv_bias=True,
                              representation_size=768,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(
            model_urls['vit_base_resnet50_224_in21k'],
            progress=False,
            map_location='cpu')
        state_dict = _conv_filter(state_dict)
        if kwargs['num_classes'] != 1000:
            del state_dict['head.weight']
            del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=strict)
    return model


def vit_custom_resnet50_224_in21k(num_blocks,
                                  num_heads,
                                  st_mode,
                                  pretrained=True,
                                  **kwargs):
    """ Hybrid model with a R50 and a Vit of custom layers .
    """
    # create a ResNetV2 w/o pre-activation, that uses StdConv and GroupNorm and has 3 stages, no head
    backbone = ResNetV2(layers=(3, 4, 9),
                        num_classes=0,
                        global_pool='',
                        in_chans=kwargs.get('in_chans', 3),
                        preact=False,
                        stem_type='same')
    model = VisionTransformer(patch_size=16,
                              embed_dim=768,
                              depth=num_blocks,
                              num_heads=num_heads,
                              hybrid_backbone=backbone,
                              mlp_ratio=4,
                              qkv_bias=True,
                              representation_size=768,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              st_mode=st_mode,
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(
            model_urls['vit_base_resnet50_224_in21k'],
            progress=False,
            map_location='cpu')
        state_dict = _conv_filter(state_dict)
        del state_dict['head.weight']
        del state_dict['head.bias']
        model.load_state_dict(state_dict, strict=False)
    return model


def vit_custom_resnet50_320_in21k(image_size,
                                  num_blocks,
                                  num_heads,
                                  st_mode,
                                  pretrained=True,
                                  **kwargs):
    """ Hybrid model with a R50 and a Vit of custom layers .
    """

    # create a ResNetV2 w/o pre-activation, that uses StdConv and GroupNorm and has 3 stages, no head
    backbone = ResNetV2(layers=(3, 4, 9),
                        num_classes=0,
                        global_pool='',
                        in_chans=kwargs.get('in_chans', 3),
                        preact=False,
                        stem_type='same')
    model = VisionTransformer(img_size=image_size,
                              patch_size=16,
                              embed_dim=768,
                              depth=num_blocks,
                              num_heads=num_heads,
                              hybrid_backbone=backbone,
                              mlp_ratio=4,
                              qkv_bias=True,
                              representation_size=768,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              st_mode=st_mode,
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(
            model_urls['vit_base_resnet50_224_in21k'],
            progress=False,
            map_location='cpu')
        state_dict = _conv_filter(state_dict)
        del state_dict['head.weight']
        del state_dict['head.bias']
        del state_dict['pos_embed']
        model.load_state_dict(state_dict, strict=False)
    return model


def vit_custom_ghostnet_224_in21k(num_blocks,
                                  num_heads,
                                  st_mode,
                                  pretrained=True,
                                  embed_dim=768,
                                  tiny=False,
                                  **kwargs):
    """ Hybrid model with a R50 and a Vit of custom layers .
    """
    # create a ResNetV2 w/o pre-activation, that uses StdConv and GroupNorm and has 3 stages, no head
    if tiny:
        backbone = get_ghostnas_tiny(flops=170)
    else:
        backbone = get_ghostnas(flops=170)
    model = VisionTransformer(patch_size=16,
                              embed_dim=embed_dim,
                              depth=num_blocks,
                              num_heads=num_heads,
                              hybrid_backbone=backbone,
                              mlp_ratio=4,
                              qkv_bias=True,
                              representation_size=embed_dim,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              st_mode=st_mode,
                              **kwargs)
    if pretrained:
        # state_dict = model_zoo.load_url(model_urls['vit_base_resnet50_224_in21k'], progress=False, map_location='cpu')
        PRETRAINED = "/apdcephfs/share_1227775/sylvainliu/data/smpldatas/ghostnas_170M_pretrain_1141226.pth"
        state_dict = torch.load(PRETRAINED)
        state_dict = _conv_filter(state_dict)
        # del state_dict['head.weight']
        # del state_dict['head.bias']
        model.patch_embed.load_state_dict(state_dict, strict=False)
    return model


def vit_custom_hrnet48_224_in21k(image_size,
                                 num_blocks,
                                 num_heads,
                                 st_mode,
                                 pretrained=True,
                                 **kwargs):
    """ Hybrid model with a R50 and a Vit of custom layers .
    """

    # create a ResNetV2 w/o pre-activation, that uses StdConv and GroupNorm and has 3 stages, no head
    backbone = get_hrnet(model_type='hrnet48', input_size=224, pretrained=True)

    model = VisionTransformer(img_size=image_size,
                              patch_size=16,
                              embed_dim=768,
                              depth=num_blocks,
                              num_heads=num_heads,
                              hybrid_backbone=backbone,
                              mlp_ratio=4,
                              qkv_bias=True,
                              representation_size=768,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              st_mode=st_mode,
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(
            model_urls['vit_base_resnet50_224_in21k'],
            progress=False,
            map_location='cpu')
        state_dict = _conv_filter(state_dict)
        model = load_state_dict(model, state_dict)
    return model


def vit_custom_hrnet48_320_in21k(image_size,
                                 num_blocks,
                                 num_heads,
                                 st_mode,
                                 pretrained=True,
                                 **kwargs):
    """ Hybrid model with a R50 and a Vit of custom layers .
    """

    # create a ResNetV2 w/o pre-activation, that uses StdConv and GroupNorm and has 3 stages, no head
    backbone = get_hrnet(model_type='hrnet48', input_size=320, pretrained=True)

    model = VisionTransformer(img_size=image_size,
                              patch_size=16,
                              embed_dim=768,
                              depth=num_blocks,
                              num_heads=num_heads,
                              hybrid_backbone=backbone,
                              mlp_ratio=4,
                              qkv_bias=True,
                              representation_size=768,
                              norm_layer=partial(nn.LayerNorm, eps=1e-6),
                              st_mode=st_mode,
                              **kwargs)
    if pretrained:
        state_dict = model_zoo.load_url(
            model_urls['vit_base_resnet50_224_in21k'],
            progress=False,
            map_location='cpu')
        state_dict = _conv_filter(state_dict)
        model = load_state_dict(model, state_dict)
    return model
