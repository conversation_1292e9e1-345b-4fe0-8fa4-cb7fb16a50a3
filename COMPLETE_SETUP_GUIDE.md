# MotionStreamer Blender插件完整安装使用指南

本指南将带你完成从零开始安装和使用MotionStreamer Blender插件的全过程。

## 🎯 系统概述

MotionStreamer Blender插件是一个完整的文本到动作生成系统，包含：
- **WebSocket服务器**: 运行MotionStreamer模型，处理文本生成动作
- **Blender插件**: 在Blender中提供用户界面，驱动MMD模型
- **实时通信**: 通过WebSocket实现Blender与服务器的实时通信

## 📋 系统要求

### 最低要求
- Windows 10/11 或 Linux
- Python 3.8+
- Blender 3.0+
- 8GB RAM
- 5GB 可用磁盘空间

### 推荐配置
- NVIDIA GPU (支持CUDA)
- 16GB+ RAM
- Python 3.9+
- Blender 3.6+

## 🚀 快速开始 (5分钟设置)

### 步骤1: 环境检查和依赖安装
```bash
# 1. 检查Python版本
python --version  # 应该显示 3.8+

# 2. 安装依赖包
python install_requirements.py
```

### 步骤2: 启动WebSocket服务器
```bash
# Windows用户 (推荐)
start_server.bat

# 或使用Python脚本
python start_server.py
```

### 步骤3: 安装Blender插件
1. 打开Blender
2. `Edit` → `Preferences` → `Add-ons`
3. 点击 `Install...`
4. 选择 `blender_addon` 文件夹 (或压缩为zip)
5. 启用 "MotionStreamer MMD Driver"

### 步骤4: 开始使用
1. 在Blender中导入MMD模型
2. 按 `N` 键打开侧边栏，找到 "MotionStreamer" 面板
3. 点击 "连接服务器"
4. 输入动作描述，如 "A person is walking"
5. 点击 "生成动作"

## 📖 详细安装步骤

### 1. 准备MotionStreamer项目

确保你已经有完整的MotionStreamer项目，包含以下必需文件：
```
MotionStreamer-main/
├── Causal_TAE/net_last.pth              # TAE模型权重
├── Experiments/latest.pth               # Transformer模型权重
├── reference_end_latent_t2m_272.npy     # 参考潜在表示
├── humanml3d_272/mean_std/Mean.npy      # 数据均值
├── humanml3d_272/mean_std/Std.npy       # 数据标准差
├── sentencet5-xxl/                      # T5文本编码模型
└── models/                              # 模型定义文件
```

### 2. 安装Python依赖

运行依赖检查和安装脚本：
```bash
python install_requirements.py
```

这个脚本会：
- ✅ 检查Python版本
- ✅ 安装必需的包 (torch, websockets, sentence-transformers等)
- ✅ 验证CUDA支持
- ✅ 检查模型文件完整性

如果遇到问题，也可以手动安装：
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install websockets numpy sentence-transformers transformers safetensors
```

### 3. 启动WebSocket服务器

有多种启动方式：

#### 方式1: 使用批处理脚本 (Windows推荐)
```bash
start_server.bat
```

#### 方式2: 使用Python启动脚本 (跨平台)
```bash
# 基本启动
python start_server.py

# 自定义配置
python start_server.py --host 0.0.0.0 --port 9000

# 跳过环境检查 (如果检查有问题)
python start_server.py --no-check
```

#### 方式3: 直接启动服务器
```bash
python websocket_server.py
```

服务器启动成功后会显示：
```
MotionStreamer WebSocket服务器已启动在 ws://localhost:8765
等待客户端连接...
```

### 4. 测试服务器连接

使用测试客户端验证服务器工作正常：
```bash
# 基本连接测试
python test_client.py

# 交互模式测试
python test_client.py --mode interactive

# 批量测试
python test_client.py --mode batch
```

### 5. 安装Blender插件

#### 方法1: 文件夹安装 (推荐)
1. 打开Blender
2. `Edit` → `Preferences` → `Add-ons`
3. 点击 `Install...` 按钮
4. 选择 `blender_addon` 文件夹
5. 在插件列表中找到 "MotionStreamer MMD Driver"
6. 勾选启用插件

#### 方法2: ZIP安装
1. 将 `blender_addon` 文件夹压缩为 `blender_addon.zip`
2. 在Blender中按上述步骤安装zip文件

### 6. 准备MMD模型

你需要一个标准的MMD模型：
1. 下载MMD模型 (.pmx文件)
2. 使用Blender的MMD插件导入模型
3. 确保模型有标准的MMD骨骼结构

推荐的MMD模型来源：
- [MMD Models](https://www.deviantart.com/mmd-models)
- [BowlRoll](https://bowlroll.net/)
- [NicoNico](https://3d.nicovideo.jp/)

## 🎮 使用指南

### 基本使用流程

1. **启动服务器**
   ```bash
   python start_server.py
   ```

2. **打开Blender并导入MMD模型**
   - 使用MMD Tools插件导入.pmx文件
   - 确保模型正确显示

3. **打开MotionStreamer面板**
   - 按 `N` 键打开3D视图侧边栏
   - 找到 "MotionStreamer" 标签页

4. **连接服务器**
   - 确认服务器地址: `localhost:8765`
   - 点击 "连接服务器" 按钮
   - 等待连接状态显示 "已连接"

5. **选择目标骨架**
   - 在 "目标设置" 中选择MMD模型的骨架对象
   - 通常名为 "Armature" 或模型名称

6. **生成动作**
   - 在 "动作描述" 框中输入文本
   - 选择动作模式 (推荐使用 "位置模式")
   - 点击 "生成动作" 按钮

7. **播放动画**
   - 动作生成完成后会自动应用到模型
   - 使用播放控制按钮控制动画
   - 调整播放速度和循环设置

### 示例动作文本

以下是一些效果良好的动作描述示例：

**基本动作**
- "A person is walking forward"
- "Someone is running quickly"
- "A man is standing still"

**复杂动作**
- "A person is dancing happily"
- "Someone is doing jumping jacks"
- "A woman is doing yoga poses"

**情感动作**
- "A person is waving goodbye"
- "Someone is clapping hands"
- "A man is stretching his arms"

### 高级功能

#### 批量生成
使用演示脚本批量生成多个动作：
```bash
python demo_blender_integration.py
```

#### 自定义骨骼映射
修改插件代码中的骨骼映射以适配不同的模型结构。

#### 动作数据导出
生成的动作可以保存为多种格式：
- `.npy` - NumPy数组格式
- `.json` - JSON格式 (包含元数据)
- `.py` - Blender脚本格式

## 🔧 故障排除

### 常见问题

#### 1. 服务器启动失败
**问题**: `ModuleNotFoundError: No module named 'torch'`
**解决**: 
```bash
pip install torch torchvision torchaudio
python install_requirements.py
```

#### 2. 连接失败
**问题**: Blender插件无法连接到服务器
**解决**:
- 确认服务器已启动并显示等待连接
- 检查防火墙设置
- 尝试使用 `127.0.0.1:8765` 而不是 `localhost:8765`

#### 3. 动作生成失败
**问题**: 服务器返回错误或生成失败
**解决**:
- 检查服务器控制台的错误信息
- 确认所有模型文件完整
- 尝试更简单的文本描述
- 重启服务器

#### 4. 动作不自然
**问题**: 生成的动作看起来不自然
**解决**:
- 使用位置模式而不是旋转模式
- 调整播放速度
- 检查MMD模型的骨骼结构
- 尝试不同的文本描述

#### 5. 性能问题
**问题**: 生成速度很慢
**解决**:
- 确认使用GPU加速 (CUDA)
- 关闭其他占用GPU的程序
- 使用更短的文本描述
- 考虑升级硬件

### 日志和调试

#### 服务器日志
查看 `motionstreamer_server.log` 文件了解详细信息：
```bash
tail -f motionstreamer_server.log
```

#### Blender控制台
在Windows上打开Blender控制台：
`Window` → `Toggle System Console`

#### 详细调试
启用详细日志模式：
```bash
python start_server.py --verbose
```

## 📚 进阶使用

### 自定义开发

#### 修改骨骼映射
编辑 `blender_addon/__init__.py` 中的 `joint_mapping` 字典：
```python
joint_mapping = {
    0: "你的骨骼名1",
    1: "你的骨骼名2",
    # ...
}
```

#### 添加新功能
1. 在服务器端添加新的命令处理
2. 在插件端添加新的操作符和UI
3. 更新通信协议

#### 性能优化
- 使用模型量化减少内存使用
- 实现动作缓存机制
- 优化WebSocket通信

### 集成到工作流

#### 与其他工具集成
- 导出为BVH格式用于其他3D软件
- 与Unity/Unreal Engine集成
- 批量处理脚本

#### 自动化流程
创建自动化脚本处理大量文本：
```python
texts = ["walking", "running", "dancing"]
for text in texts:
    # 生成动作
    # 保存文件
    # 应用到模型
```

## 🎉 完成！

恭喜！你现在已经成功设置了完整的MotionStreamer Blender插件系统。

### 下一步
- 尝试不同的动作描述文本
- 导入更多MMD模型进行测试
- 探索高级功能和自定义选项
- 分享你的创作成果！

### 获取帮助
- 查看 `README_BlenderPlugin.md` 了解详细功能
- 查看 `PROJECT_STRUCTURE.md` 了解技术细节
- 使用 `test_client.py` 进行故障诊断
- 在项目仓库中提交Issue

祝你使用愉快！🎭✨
