"""
MMD驱动器测试插件
用于测试动作数据应用功能
"""

bl_info = {
    "name": "MMD Driver Test",
    "author": "MotionStreamer Team",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > MMD Test",
    "description": "测试MMD动作驱动功能",
    "category": "Animation",
}

import bpy
import json
import os
from bpy.props import StringProperty, BoolProperty
from bpy.types import Panel, Operator, PropertyGroup

# 导入MMD驱动模块
try:
    from . import mmd_driver
    print("MMD驱动模块加载成功")
except ImportError as e:
    print(f"MMD驱动模块加载失败: {e}")
    mmd_driver = None

class MMDTestProperties(PropertyGroup):
    """MMD测试属性组"""
    
    target_armature: StringProperty(
        name="目标骨架",
        description="选择要驱动的MMD模型骨架",
        default=""
    )
    
    test_data_path: StringProperty(
        name="测试数据路径",
        description="测试动作数据文件路径",
        default="test_motion_data.json",
        subtype='FILE_PATH'
    )
    
    auto_play: BoolProperty(
        name="自动播放",
        description="应用动作后自动播放",
        default=True
    )

class MMD_OT_load_test_data(Operator):
    """加载测试数据"""
    bl_idname = "mmd_test.load_test_data"
    bl_label = "加载测试数据"
    bl_description = "加载测试动作数据并应用到MMD模型"
    
    def execute(self, context):
        if not mmd_driver:
            self.report({'ERROR'}, "MMD驱动模块未加载")
            return {'CANCELLED'}
        
        props = context.scene.mmd_test_props
        
        # 检查文件是否存在
        if not os.path.exists(props.test_data_path):
            self.report({'ERROR'}, f"测试数据文件不存在: {props.test_data_path}")
            return {'CANCELLED'}
        
        try:
            # 加载测试数据
            with open(props.test_data_path, 'r') as f:
                motion_data = json.load(f)
            
            print(f"加载了{len(motion_data)}帧动作数据")
            
            # 获取MMD驱动器
            driver = mmd_driver.get_mmd_driver()
            
            # 查找目标骨架
            armature = driver.find_armature(props.target_armature)
            
            if not armature:
                self.report({'ERROR'}, "未找到目标骨架")
                return {'CANCELLED'}
            
            # 应用动作数据
            success = driver.apply_motion_data(armature, motion_data, 'pos')
            
            if success:
                if props.auto_play:
                    bpy.ops.screen.animation_play()
                self.report({'INFO'}, f"测试数据已成功应用到骨架: {armature.name}")
            else:
                self.report({'ERROR'}, "动作数据应用失败")
            
        except Exception as e:
            self.report({'ERROR'}, f"加载测试数据时出错: {e}")
            print(f"详细错误: {e}")
            import traceback
            traceback.print_exc()
        
        return {'FINISHED'}

class MMD_OT_generate_test_data(Operator):
    """生成测试数据"""
    bl_idname = "mmd_test.generate_test_data"
    bl_label = "生成测试数据"
    bl_description = "生成简单的测试动作数据"
    
    def execute(self, context):
        try:
            import numpy as np
            
            # 生成简单的测试动作
            frames = 60
            joints = 22
            motion_data = []
            
            for frame in range(frames):
                frame_data = []
                
                for joint in range(joints):
                    t = frame / frames * 2 * np.pi
                    
                    if joint == 0:  # 根关节
                        x = frame * 0.02
                        y = 0.0
                        z = np.sin(t * 2) * 0.05
                    elif joint in [16, 19]:  # 腿部
                        x = 0.0
                        y = np.sin(t + (np.pi if joint == 19 else 0)) * 0.1
                        z = 0.0
                    elif joint in [9, 13]:  # 手臂
                        x = np.sin(t + (np.pi if joint == 13 else 0)) * 0.08
                        y = 0.0
                        z = 0.0
                    else:
                        x = np.sin(t) * 0.01
                        y = 0.0
                        z = 0.0
                    
                    frame_data.append([x, y, z])
                
                motion_data.append(frame_data)
            
            # 保存测试数据
            props = context.scene.mmd_test_props
            with open(props.test_data_path, 'w') as f:
                json.dump(motion_data, f, indent=2)
            
            self.report({'INFO'}, f"测试数据已生成: {props.test_data_path}")
            
        except Exception as e:
            self.report({'ERROR'}, f"生成测试数据时出错: {e}")
        
        return {'FINISHED'}

class MMD_PT_test_panel(Panel):
    """MMD测试面板"""
    bl_label = "MMD Driver Test"
    bl_idname = "MMD_PT_test_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MMD Test"
    
    def draw(self, context):
        layout = self.layout
        props = context.scene.mmd_test_props
        
        # 目标设置
        box = layout.box()
        box.label(text="目标设置", icon='ARMATURE_DATA')
        box.prop_search(props, "target_armature", bpy.data, "objects")
        
        # 测试数据
        box = layout.box()
        box.label(text="测试数据", icon='FILE')
        box.prop(props, "test_data_path")
        
        row = box.row()
        row.operator("mmd_test.generate_test_data", icon='ADD')
        row.operator("mmd_test.load_test_data", icon='PLAY')
        
        # 播放设置
        box = layout.box()
        box.label(text="播放设置", icon='SETTINGS')
        box.prop(props, "auto_play")
        
        # 状态信息
        if mmd_driver:
            layout.label(text="MMD驱动器: 已加载", icon='CHECKMARK')
        else:
            layout.label(text="MMD驱动器: 未加载", icon='ERROR')

# 注册类列表
classes = [
    MMDTestProperties,
    MMD_OT_load_test_data,
    MMD_OT_generate_test_data,
    MMD_PT_test_panel,
]

def register():
    """注册插件"""
    try:
        for cls in classes:
            bpy.utils.register_class(cls)
        
        bpy.types.Scene.mmd_test_props = bpy.props.PointerProperty(type=MMDTestProperties)
        print("MMD测试插件注册成功")
    except Exception as e:
        print(f"MMD测试插件注册失败: {e}")

def unregister():
    """注销插件"""
    try:
        for cls in reversed(classes):
            bpy.utils.unregister_class(cls)
        
        if hasattr(bpy.types.Scene, 'mmd_test_props'):
            del bpy.types.Scene.mmd_test_props
        print("MMD测试插件注销成功")
    except Exception as e:
        print(f"MMD测试插件注销失败: {e}")

if __name__ == "__main__":
    register()
