#!/usr/bin/env python3
"""
MotionStreamer WebSocket服务器启动脚本
提供更友好的启动界面和配置选项
"""

import argparse
import asyncio
import os
import sys
import signal
from websocket_server import MotionStreamerServer

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print('\n\n🛑 收到停止信号，正在关闭服务器...')
    sys.exit(0)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 检查必需的包
    required_packages = ['torch', 'websockets', 'numpy', 'sentence_transformers']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必需的包: {', '.join(missing_packages)}")
        print("请运行: python install_requirements.py")
        return False
    
    # 检查模型文件
    model_files = [
        "Causal_TAE/net_last.pth",
        "Experiments/latest.pth", 
        "reference_end_latent_t2m_272.npy",
        "humanml3d_272/mean_std/Mean.npy",
        "humanml3d_272/mean_std/Std.npy"
    ]
    
    missing_files = []
    for file_path in model_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少模型文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("请确保已下载所有必需的模型文件")
        return False
    
    print("✅ 环境检查通过")
    return True

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    MotionStreamer Server                     ║
║                  文本到动作生成WebSocket服务器                ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_usage_info(host, port):
    """打印使用信息"""
    print(f"""
📋 服务器信息:
   • WebSocket地址: ws://{host}:{port}
   • 支持的命令: generate_motion, ping
   • 动作模式: pos (位置), rot (旋转)

🎯 Blender插件连接:
   1. 在Blender中安装MotionStreamer插件
   2. 打开3D视图侧边栏的MotionStreamer面板
   3. 设置服务器地址为 {host}:{port}
   4. 点击"连接服务器"按钮

💡 示例文本:
   • "A person is walking forward"
   • "Someone is dancing happily" 
   • "A man is running and jumping"
   • "A woman is doing yoga poses"

⚠️  注意事项:
   • 首次加载模型需要较长时间
   • 建议使用GPU加速以获得更好性能
   • 按Ctrl+C停止服务器

""")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='MotionStreamer WebSocket服务器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start_server.py                    # 使用默认设置启动
  python start_server.py --host 0.0.0.0    # 允许外部连接
  python start_server.py --port 9000       # 使用自定义端口
  python start_server.py --no-check        # 跳过环境检查
        """
    )
    
    parser.add_argument(
        '--host', 
        default='localhost',
        help='服务器绑定地址 (默认: localhost)'
    )
    
    parser.add_argument(
        '--port', 
        type=int, 
        default=8765,
        help='服务器端口 (默认: 8765)'
    )
    
    parser.add_argument(
        '--no-check', 
        action='store_true',
        help='跳过环境检查'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true',
        help='显示详细日志'
    )
    
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    # 打印横幅
    print_banner()
    
    # 环境检查
    if not args.no_check:
        if not check_environment():
            print("\n❌ 环境检查失败，服务器无法启动")
            print("使用 --no-check 参数可跳过检查（不推荐）")
            sys.exit(1)
    else:
        print("⚠️  跳过环境检查")
    
    # 打印使用信息
    print_usage_info(args.host, args.port)
    
    try:
        # 创建并启动服务器
        print("🚀 正在启动MotionStreamer服务器...")
        server = MotionStreamerServer()
        
        # 启动异步服务器
        asyncio.run(server.start_server(args.host, args.port))
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
