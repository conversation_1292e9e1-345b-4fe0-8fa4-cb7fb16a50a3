
# Blender中使用MMD驱动器的步骤

## 1. 准备工作
1. 打开Blender
2. 导入一个MMD模型（.pmx文件）
3. 确保模型有骨架对象

## 2. 安装插件
1. 转到 Edit → Preferences → Add-ons
2. 点击 "Install..." 
3. 选择 blender_addon/__init__0.py 文件
4. 启用 "MotionStreamer MMD Driver" 插件

## 3. 测试动作应用
### 方法A: 使用测试插件
1. 安装 blender_addon/test_mmd_driver.py
2. 在侧边栏找到 "MMD Test" 面板
3. 选择目标骨架
4. 点击 "生成测试数据"
5. 点击 "加载测试数据"

### 方法B: 使用完整功能
1. 启动 websocket_server.py
2. 在 "MotionStreamer" 面板中连接服务器
3. 输入动作描述
4. 选择目标骨架
5. 点击 "生成动作"

## 4. 预期结果
- 模型应该开始执行走路动作
- 根关节向前移动
- 腿部交替抬起
- 手臂摆动
- 头部轻微摆动

## 5. 故障排除
- 如果模型不动，检查骨架选择是否正确
- 查看Blender控制台的错误信息
- 确认动作数据格式正确
