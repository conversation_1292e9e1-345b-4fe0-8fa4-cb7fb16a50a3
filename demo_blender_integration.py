#!/usr/bin/env python3
"""
MotionStreamer Blender集成演示脚本
展示如何在Python中直接使用MotionStreamer生成动作并保存为Blender可用的格式
"""

import os
import sys
import torch
import numpy as np
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.llama_model import LLaMAHF, LLaMAHFConfig
import models.tae as tae
import options.option_transformer as option_trans
from sentence_transformers import SentenceTransformer
from visualization.recover_visualize import recover_from_local_position
import warnings
warnings.filterwarnings('ignore')

class MotionStreamerDemo:
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        self.setup_models()
    
    def setup_models(self):
        """初始化模型"""
        print("正在初始化模型...")
        
        # 获取参数
        args = option_trans.get_args_parser()
        torch.manual_seed(args.seed)
        
        # 加载T5模型
        print("加载T5文本编码模型...")
        self.t5_model = SentenceTransformer('sentencet5-xxl/')
        self.t5_model.eval()
        for p in self.t5_model.parameters():
            p.requires_grad = False
        
        # 设置网络参数
        clip_range = [-30, 20]
        
        # 初始化TAE网络
        print("初始化TAE网络...")
        self.net = tae.Causal_HumanTAE(
            hidden_size=args.hidden_size,
            down_t=args.down_t,
            stride_t=args.stride_t,
            depth=args.depth,
            dilation_growth_rate=args.dilation_growth_rate,
            activation='relu',
            latent_dim=args.latent_dim,
            clip_range=clip_range
        )
        
        # 初始化Transformer
        print("初始化Transformer模型...")
        config = LLaMAHFConfig.from_name('Normal_size')
        config.block_size = 78
        self.trans_encoder = LLaMAHF(config, args.num_diffusion_head_layers, args.latent_dim, self.device)
        
        # 加载检查点
        print(f'加载TAE检查点: {args.resume_pth}')
        ckpt = torch.load(args.resume_pth, map_location='cpu')
        self.net.load_state_dict(ckpt['net'], strict=True)
        self.net.eval()
        self.net.to(self.device)
        
        if args.resume_trans is not None:
            print(f'加载Transformer检查点: {args.resume_trans}')
            ckpt = torch.load(args.resume_trans, map_location='cpu')
            new_ckpt_trans = {}
            for key in ckpt['trans'].keys():
                if key.split('.')[0] == 'module':
                    new_key = '.'.join(key.split('.')[1:])
                else:
                    new_key = key
                new_ckpt_trans[new_key] = ckpt['trans'][key]
            self.trans_encoder.load_state_dict(new_ckpt_trans, strict=True)
        
        self.trans_encoder.eval()
        self.trans_encoder.to(self.device)
        
        # 加载参考数据
        print("加载参考数据...")
        self.reference_end_latent = np.load('reference_end_latent_t2m_272.npy')
        self.reference_end_latent = torch.from_numpy(self.reference_end_latent).to(self.device)
        
        # 加载均值和标准差
        self.mean = np.load('humanml3d_272/mean_std/Mean.npy')
        self.std = np.load('humanml3d_272/mean_std/Std.npy')
        
        print("模型初始化完成!")
    
    def generate_motion(self, text, mode='pos'):
        """生成动作"""
        print(f"生成动作: '{text}' (模式: {mode})")
        
        # 生成动作潜在表示
        threshold = 0.1
        motion_latents = self.trans_encoder.sample_for_eval_CFG_inference(
            text=text, 
            tokenizer=self.t5_model, 
            device=self.device, 
            reference_end_latent=self.reference_end_latent, 
            threshold=threshold
        )
        
        # 解码动作序列
        motion_seqs = self.net.forward_decoder(motion_latents)
        motion = motion_seqs.squeeze(0)
        motion = motion.detach().cpu().numpy()
        
        if mode == 'pos':
            # 恢复关节位置
            pred_xyz = recover_from_local_position(motion * self.std + self.mean, 22)
            return pred_xyz, motion
        else:
            # 返回原始272维表示
            return motion, motion
    
    def save_motion_data(self, motion_data, text, mode, output_dir='demo_output'):
        """保存动作数据为多种格式"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_text = "".join(c for c in text if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_text = safe_text.replace(' ', '_')[:50]  # 限制文件名长度
        
        base_filename = f"{safe_text}_{mode}_{timestamp}"
        
        # 保存为numpy格式
        np_file = os.path.join(output_dir, f"{base_filename}.npy")
        np.save(np_file, motion_data)
        print(f"保存numpy文件: {np_file}")
        
        # 保存为JSON格式 (用于Blender插件)
        json_file = os.path.join(output_dir, f"{base_filename}.json")
        motion_json = {
            "text": text,
            "mode": mode,
            "timestamp": timestamp,
            "frames": len(motion_data),
            "joints": len(motion_data[0]) if len(motion_data) > 0 else 0,
            "motion_data": motion_data.tolist()
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(motion_json, f, ensure_ascii=False, indent=2)
        print(f"保存JSON文件: {json_file}")
        
        # 保存元数据
        meta_file = os.path.join(output_dir, f"{base_filename}_meta.txt")
        with open(meta_file, 'w', encoding='utf-8') as f:
            f.write(f"动作描述: {text}\n")
            f.write(f"生成模式: {mode}\n")
            f.write(f"生成时间: {timestamp}\n")
            f.write(f"数据形状: {motion_data.shape}\n")
            f.write(f"帧数: {len(motion_data)}\n")
            if mode == 'pos':
                f.write(f"关节数: {len(motion_data[0])}\n")
                f.write(f"坐标维度: 3D (x, y, z)\n")
            else:
                f.write(f"特征维度: 272\n")
        print(f"保存元数据: {meta_file}")
        
        return {
            'numpy_file': np_file,
            'json_file': json_file,
            'meta_file': meta_file
        }
    
    def generate_blender_script(self, json_file, output_dir='demo_output'):
        """生成Blender导入脚本"""
        script_content = f'''
import bpy
import json
import mathutils
from mathutils import Vector

# 加载动作数据
with open(r"{os.path.abspath(json_file)}", 'r', encoding='utf-8') as f:
    motion_data = json.load(f)

print(f"加载动作: {{motion_data['text']}}")
print(f"帧数: {{motion_data['frames']}}, 关节数: {{motion_data['joints']}}")

# 获取当前选中的骨架
armature = bpy.context.active_object
if not armature or armature.type != 'ARMATURE':
    print("请选择一个骨架对象")
else:
    # 设置动画帧范围
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = motion_data['frames']
    
    # 进入姿态模式
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='POSE')
    
    # 清除现有动画
    if armature.animation_data:
        armature.animation_data_clear()
    
    # 创建新动作
    action = bpy.data.actions.new(name=f"MotionStreamer_{{motion_data['text'][:20]}}")
    armature.animation_data_create()
    armature.animation_data.action = action
    
    # MMD骨骼映射
    joint_mapping = {{
        0: "センター", 1: "上半身", 2: "上半身", 3: "上半身2", 4: "首", 5: "頭",
        6: "頭", 7: "頭", 8: "左肩", 9: "左腕", 10: "左ひじ", 11: "左手首",
        12: "右肩", 13: "右腕", 14: "右ひじ", 15: "右手首", 16: "左足",
        17: "左ひざ", 18: "左足首", 19: "右足", 20: "右ひざ", 21: "右足首"
    }}
    
    # 应用动作数据
    for frame_idx, frame_data in enumerate(motion_data['motion_data']):
        bpy.context.scene.frame_set(frame_idx + 1)
        
        for joint_idx, position in enumerate(frame_data):
            if joint_idx < len(joint_mapping):
                bone_name = joint_mapping.get(joint_idx)
                if bone_name and bone_name in armature.pose.bones:
                    bone = armature.pose.bones[bone_name]
                    bone.location = Vector(position)
                    bone.keyframe_insert(data_path="location")
    
    print("动作应用完成!")
    bpy.ops.screen.animation_play()
'''
        
        script_file = os.path.join(output_dir, f"import_motion_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py")
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"生成Blender脚本: {script_file}")
        return script_file

def main():
    """主函数"""
    print("🎭 MotionStreamer Blender集成演示")
    print("=" * 50)
    
    # 示例文本列表
    demo_texts = [
        "A person is walking forward",
        "Someone is dancing happily", 
        "A man is running and jumping",
        "A woman is doing yoga poses",
        "A person is waving hands"
    ]
    
    try:
        # 初始化演示系统
        demo = MotionStreamerDemo()
        
        print("\n📝 可用的演示文本:")
        for i, text in enumerate(demo_texts, 1):
            print(f"  {i}. {text}")
        
        # 用户选择
        while True:
            try:
                choice = input(f"\n请选择文本 (1-{len(demo_texts)}) 或输入自定义文本: ").strip()
                
                if choice.isdigit() and 1 <= int(choice) <= len(demo_texts):
                    selected_text = demo_texts[int(choice) - 1]
                    break
                elif choice:
                    selected_text = choice
                    break
                else:
                    print("请输入有效选择")
            except KeyboardInterrupt:
                print("\n👋 演示已取消")
                return
        
        # 选择模式
        mode = input("选择模式 (pos/rot, 默认pos): ").strip().lower()
        if mode not in ['pos', 'rot']:
            mode = 'pos'
        
        print(f"\n🎬 生成动作: '{selected_text}' (模式: {mode})")
        
        # 生成动作
        motion_data, raw_motion = demo.generate_motion(selected_text, mode)
        
        print(f"✅ 动作生成完成!")
        print(f"   数据形状: {motion_data.shape}")
        print(f"   帧数: {len(motion_data)}")
        
        # 保存数据
        print("\n💾 保存动作数据...")
        files = demo.save_motion_data(motion_data, selected_text, mode)
        
        # 生成Blender脚本
        if mode == 'pos':
            print("\n📜 生成Blender导入脚本...")
            script_file = demo.generate_blender_script(files['json_file'])
            
            print(f"\n🎯 使用方法:")
            print(f"1. 在Blender中导入MMD模型")
            print(f"2. 选择骨架对象")
            print(f"3. 在Blender文本编辑器中打开: {script_file}")
            print(f"4. 运行脚本应用动作")
        
        print(f"\n📁 输出文件:")
        for key, file_path in files.items():
            print(f"   {key}: {file_path}")
        
        print(f"\n🎉 演示完成!")
        
    except KeyboardInterrupt:
        print("\n👋 演示已取消")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
