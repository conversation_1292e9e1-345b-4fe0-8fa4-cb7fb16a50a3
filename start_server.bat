@echo off
chcp 65001 >nul
title MotionStreamer WebSocket服务器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    MotionStreamer Server                     ║
echo ║                  文本到动作生成WebSocket服务器                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🚀 启动MotionStreamer服务器...
echo 💡 按Ctrl+C停止服务器
echo.

python start_server.py

echo.
echo 🛑 服务器已停止
pause
