#!/usr/bin/env python3
"""
MotionStreamer WebSocket客户端测试工具
用于测试WebSocket服务器的功能
"""

import asyncio
import websockets
import json
import time
import argparse

class MotionStreamerTestClient:
    def __init__(self, host='localhost', port=8765):
        self.host = host
        self.port = port
        self.uri = f"ws://{host}:{port}"
        
    async def test_connection(self):
        """测试连接"""
        print(f"🔗 测试连接到 {self.uri}")
        
        try:
            async with websockets.connect(self.uri) as websocket:
                print("✅ 连接成功")
                
                # 发送ping测试
                ping_request = {"command": "ping"}
                await websocket.send(json.dumps(ping_request))
                
                response = await websocket.recv()
                data = json.loads(response)
                
                if data.get('status') == 'pong':
                    print("✅ Ping测试成功")
                    return True
                else:
                    print(f"❌ Ping测试失败: {data}")
                    return False
                    
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def test_motion_generation(self, text, mode='pos'):
        """测试动作生成"""
        print(f"🎭 测试动作生成: '{text}' (模式: {mode})")
        
        try:
            async with websockets.connect(self.uri) as websocket:
                # 发送动作生成请求
                request = {
                    "command": "generate_motion",
                    "text": text,
                    "mode": mode
                }
                
                print("📤 发送请求...")
                start_time = time.time()
                await websocket.send(json.dumps(request))
                
                print("⏳ 等待响应...")
                response = await websocket.recv()
                elapsed_time = time.time() - start_time
                
                data = json.loads(response)
                
                if data.get('status') == 'success':
                    frames = data.get('frames', 0)
                    joints = data.get('joints', 0)
                    print(f"✅ 动作生成成功!")
                    print(f"   📊 数据信息: {frames} 帧, {joints} 关节/维度")
                    print(f"   ⏱️  生成时间: {elapsed_time:.2f} 秒")
                    return True
                else:
                    error_msg = data.get('message', '未知错误')
                    print(f"❌ 动作生成失败: {error_msg}")
                    return False
                    
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎮 进入交互模式 (输入 'quit' 退出)")
        
        try:
            async with websockets.connect(self.uri) as websocket:
                print("✅ 已连接到服务器")
                
                while True:
                    try:
                        # 获取用户输入
                        text = input("\n💬 请输入动作描述 (或 'quit' 退出): ").strip()
                        
                        if text.lower() in ['quit', 'exit', 'q']:
                            break
                        
                        if not text:
                            continue
                        
                        # 选择模式
                        mode = input("🎯 选择模式 (pos/rot, 默认pos): ").strip().lower()
                        if mode not in ['pos', 'rot']:
                            mode = 'pos'
                        
                        # 发送请求
                        request = {
                            "command": "generate_motion",
                            "text": text,
                            "mode": mode
                        }
                        
                        print("📤 发送请求...")
                        start_time = time.time()
                        await websocket.send(json.dumps(request))
                        
                        print("⏳ 生成中...")
                        response = await websocket.recv()
                        elapsed_time = time.time() - start_time
                        
                        data = json.loads(response)
                        
                        if data.get('status') == 'success':
                            frames = data.get('frames', 0)
                            joints = data.get('joints', 0)
                            print(f"✅ 生成成功! {frames} 帧, {joints} 关节/维度, 耗时 {elapsed_time:.2f} 秒")
                        else:
                            error_msg = data.get('message', '未知错误')
                            print(f"❌ 生成失败: {error_msg}")
                            
                    except KeyboardInterrupt:
                        break
                    except Exception as e:
                        print(f"❌ 处理请求时出错: {e}")
                
                print("👋 退出交互模式")
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
    
    async def batch_test(self, test_cases):
        """批量测试"""
        print(f"🧪 开始批量测试 ({len(test_cases)} 个测试用例)")
        
        success_count = 0
        total_time = 0
        
        for i, (text, mode) in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}/{len(test_cases)}: '{text}' ({mode})")
            
            start_time = time.time()
            success = await self.test_motion_generation(text, mode)
            elapsed_time = time.time() - start_time
            
            if success:
                success_count += 1
            
            total_time += elapsed_time
            
            # 短暂延迟避免服务器过载
            await asyncio.sleep(1)
        
        print(f"\n📊 批量测试结果:")
        print(f"   ✅ 成功: {success_count}/{len(test_cases)}")
        print(f"   ❌ 失败: {len(test_cases) - success_count}/{len(test_cases)}")
        print(f"   ⏱️  总时间: {total_time:.2f} 秒")
        print(f"   📈 平均时间: {total_time/len(test_cases):.2f} 秒/测试")

def main():
    parser = argparse.ArgumentParser(description='MotionStreamer WebSocket客户端测试工具')
    
    parser.add_argument('--host', default='localhost', help='服务器地址')
    parser.add_argument('--port', type=int, default=8765, help='服务器端口')
    parser.add_argument('--mode', choices=['test', 'interactive', 'batch'], 
                       default='test', help='运行模式')
    parser.add_argument('--text', default='A person is walking forward', 
                       help='测试文本 (仅test模式)')
    parser.add_argument('--motion-mode', choices=['pos', 'rot'], default='pos',
                       help='动作模式 (仅test模式)')
    
    args = parser.parse_args()
    
    client = MotionStreamerTestClient(args.host, args.port)
    
    print("🚀 MotionStreamer WebSocket客户端测试工具")
    print(f"🎯 目标服务器: ws://{args.host}:{args.port}")
    print()
    
    try:
        if args.mode == 'test':
            # 单次测试模式
            async def run_test():
                # 先测试连接
                if await client.test_connection():
                    # 再测试动作生成
                    await client.test_motion_generation(args.text, args.motion_mode)
            
            asyncio.run(run_test())
            
        elif args.mode == 'interactive':
            # 交互模式
            asyncio.run(client.interactive_mode())
            
        elif args.mode == 'batch':
            # 批量测试模式
            test_cases = [
                ("A person is walking forward", "pos"),
                ("Someone is dancing happily", "pos"),
                ("A man is running and jumping", "pos"),
                ("A woman is doing yoga poses", "pos"),
                ("A person is waving hands", "pos"),
                ("Someone is sitting down", "rot"),
                ("A person is climbing stairs", "rot"),
            ]
            
            async def run_batch():
                if await client.test_connection():
                    await client.batch_test(test_cases)
            
            asyncio.run(run_batch())
    
    except KeyboardInterrupt:
        print("\n👋 测试已取消")
    except Exception as e:
        print(f"❌ 测试出错: {e}")

if __name__ == "__main__":
    main()
