"""
MMD模型驱动模块
负责将动作数据应用到Blender中的MMD模型
"""

import bpy
import mathutils
import numpy as np
import time
from mathutils import Vector, Euler, Quaternion, Matrix


class MMDDriver:
    """MMD模型驱动器"""
    
    def __init__(self):
        # HumanML3D的22个关节到MMD骨骼的映射
        self.joint_to_mmd_mapping = {
            0: "センター",          # pelvis/root
            1: "下半身",            # left_hip (用下半身代替)
            2: "下半身",            # right_hip (用下半身代替)
            3: "上半身",            # spine1
            4: "上半身2",           # spine2
            5: "首",               # spine3/neck
            6: "首",               # neck
            7: "頭",               # head
            8: "左肩",             # left_collar
            9: "左腕",             # left_shoulder
            10: "左ひじ",          # left_elbow
            11: "左手首",          # left_wrist
            12: "右肩",            # right_collar
            13: "右腕",            # right_shoulder
            14: "右ひじ",          # right_elbow
            15: "右手首",          # right_wrist
            16: "左足",            # left_hip
            17: "左ひざ",          # left_knee
            18: "左足首",          # left_ankle
            19: "右足",            # right_hip
            20: "右ひざ",          # right_knee
            21: "右足首",          # right_ankle
        }
        
        # 备用骨骼名称映射（适应不同的MMD模型）
        self.alternative_bone_names = {
            "センター": ["center", "Center", "root", "Root"],
            "下半身": ["lower body", "Lower Body", "hip", "Hip"],
            "上半身": ["upper body", "Upper Body", "spine", "Spine"],
            "上半身2": ["upper body2", "Upper Body2", "spine1", "Spine1"],
            "首": ["neck", "Neck"],
            "頭": ["head", "Head"],
            "左肩": ["left shoulder", "Left Shoulder", "shoulder_L", "Shoulder_L"],
            "左腕": ["left arm", "Left Arm", "arm_L", "Arm_L"],
            "左ひじ": ["left elbow", "Left Elbow", "elbow_L", "Elbow_L"],
            "左手首": ["left wrist", "Left Wrist", "wrist_L", "Wrist_L"],
            "右肩": ["right shoulder", "Right Shoulder", "shoulder_R", "Shoulder_R"],
            "右腕": ["right arm", "Right Arm", "arm_R", "Arm_R"],
            "右ひじ": ["right elbow", "Right Elbow", "elbow_R", "Elbow_R"],
            "右手首": ["right wrist", "Right Wrist", "wrist_R", "Wrist_R"],
            "左足": ["left leg", "Left Leg", "leg_L", "Leg_L"],
            "左ひざ": ["left knee", "Left Knee", "knee_L", "Knee_L"],
            "左足首": ["left ankle", "Left Ankle", "ankle_L", "Ankle_L"],
            "右足": ["right leg", "Right Leg", "leg_R", "Leg_R"],
            "右ひざ": ["right knee", "Right Knee", "knee_R", "Knee_R"],
            "右足首": ["right ankle", "Right Ankle", "ankle_R", "Ankle_R"],
        }
    
    def find_armature(self, target_name=None):
        """查找目标骨架对象"""
        if target_name:
            armature = bpy.data.objects.get(target_name)
            if armature and armature.type == 'ARMATURE':
                return armature
        
        # 自动查找第一个骨架对象
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE':
                return obj
        
        return None
    
    def find_bone_name(self, armature, target_bone_name):
        """查找实际存在的骨骼名称"""
        if not armature or not armature.pose:
            return None
            
        # 首先尝试直接匹配
        if target_bone_name in armature.pose.bones:
            return target_bone_name
        
        # 尝试备用名称
        alternatives = self.alternative_bone_names.get(target_bone_name, [])
        for alt_name in alternatives:
            if alt_name in armature.pose.bones:
                return alt_name
        
        return None
    
    def apply_motion_data(self, armature, motion_data, mode='pos'):
        """将动作数据应用到骨架对象"""
        if not motion_data or not armature:
            print("MMDDriver: 没有动作数据或骨架对象")
            return False
        
        try:
            # 设置动画帧范围
            frame_count = len(motion_data)
            bpy.context.scene.frame_start = 1
            bpy.context.scene.frame_end = frame_count
            
            # 选择并激活骨架
            bpy.context.view_layer.objects.active = armature
            bpy.ops.object.mode_set(mode='POSE')
            
            # 清除现有动画数据
            if armature.animation_data:
                armature.animation_data_clear()
            
            # 创建新的动作
            action = bpy.data.actions.new(name=f"MotionStreamer_{int(time.time())}")
            armature.animation_data_create()
            armature.animation_data.action = action
            
            if mode == 'pos':
                return self._apply_position_data(armature, motion_data)
            else:
                return self._apply_rotation_data(armature, motion_data)
                
        except Exception as e:
            print(f"MMDDriver: 应用动作数据时出错 - {e}")
            return False
    
    def _apply_position_data(self, armature, motion_data):
        """应用位置数据到骨架"""
        print(f"MMDDriver: 开始应用位置数据，共{len(motion_data)}帧")
        
        # 获取第一帧数据来确定数据格式
        if not motion_data or len(motion_data[0]) == 0:
            print("MMDDriver: 动作数据为空")
            return False
        
        first_frame = motion_data[0]
        joints_count = len(first_frame)
        
        print(f"MMDDriver: 检测到{joints_count}个关节的数据")
        
        # 如果是22个关节的3D位置数据 (22*3=66维)
        if joints_count == 66:
            return self._apply_joint_positions(armature, motion_data)
        # 如果是22个关节的位置数据，每个关节3个坐标
        elif joints_count == 22 and len(motion_data[0][0]) == 3:
            return self._apply_joint_positions_3d(armature, motion_data)
        else:
            print(f"MMDDriver: 不支持的数据格式，关节数: {joints_count}")
            return False
    
    def _apply_joint_positions_3d(self, armature, motion_data):
        """应用3D关节位置数据"""
        applied_bones = 0
        
        for frame_idx, frame_data in enumerate(motion_data):
            bpy.context.scene.frame_set(frame_idx + 1)
            
            for joint_idx, position in enumerate(frame_data):
                if joint_idx in self.joint_to_mmd_mapping:
                    target_bone_name = self.joint_to_mmd_mapping[joint_idx]
                    actual_bone_name = self.find_bone_name(armature, target_bone_name)
                    
                    if actual_bone_name:
                        bone = armature.pose.bones[actual_bone_name]
                        
                        # 转换坐标系：HumanML3D使用Y-up，Blender使用Z-up
                        # HumanML3D: X-right, Y-up, Z-forward
                        # Blender: X-right, Y-forward, Z-up
                        blender_pos = Vector((
                            position[0],    # X保持不变
                            -position[2],   # Z变为-Y (前后翻转)
                            position[1]     # Y变为Z
                        ))
                        
                        # 应用缩放因子
                        blender_pos *= 0.01  # 缩小100倍
                        
                        # 设置骨骼位置
                        if joint_idx == 0:  # 根骨骼使用location
                            bone.location = blender_pos
                            bone.keyframe_insert(data_path="location")
                        else:  # 其他骨骼可能需要使用不同的变换方式
                            # 对于非根骨骼，我们可能需要计算相对变换
                            bone.location = blender_pos * 0.1  # 进一步缩小
                            bone.keyframe_insert(data_path="location")
                        
                        if frame_idx == 0:  # 只在第一帧打印
                            applied_bones += 1
        
        print(f"MMDDriver: 成功应用了{applied_bones}个骨骼的动画")
        return applied_bones > 0
    
    def _apply_joint_positions(self, armature, motion_data):
        """应用展平的关节位置数据 (66维)"""
        applied_bones = 0
        
        for frame_idx, frame_data in enumerate(motion_data):
            bpy.context.scene.frame_set(frame_idx + 1)
            
            # 将66维数据重新整形为22个关节的3D位置
            positions = np.array(frame_data).reshape(22, 3)
            
            for joint_idx in range(22):
                if joint_idx in self.joint_to_mmd_mapping:
                    target_bone_name = self.joint_to_mmd_mapping[joint_idx]
                    actual_bone_name = self.find_bone_name(armature, target_bone_name)
                    
                    if actual_bone_name:
                        bone = armature.pose.bones[actual_bone_name]
                        position = positions[joint_idx]
                        
                        # 坐标系转换和缩放
                        blender_pos = Vector((
                            position[0],
                            -position[2],
                            position[1]
                        )) * 0.01
                        
                        bone.location = blender_pos
                        bone.keyframe_insert(data_path="location")
                        
                        if frame_idx == 0:
                            applied_bones += 1
        
        print(f"MMDDriver: 成功应用了{applied_bones}个骨骼的动画")
        return applied_bones > 0
    
    def _apply_rotation_data(self, armature, motion_data):
        """应用旋转数据到骨架（272维数据）"""
        print(f"MMDDriver: 开始应用旋转数据，共{len(motion_data)}帧")
        
        applied_bones = 0
        
        for frame_idx, frame_data in enumerate(motion_data):
            bpy.context.scene.frame_set(frame_idx + 1)
            
            # 简化处理：只处理根骨骼的移动
            center_bone_name = self.find_bone_name(armature, "センター")
            if center_bone_name:
                root_bone = armature.pose.bones[center_bone_name]
                
                # 从272维数据中提取根骨骼的位置信息
                if len(frame_data) >= 2:
                    # 前两维是根骨骼的XZ速度
                    root_bone.location.x = frame_data[0] * 0.01
                    root_bone.location.y = frame_data[1] * 0.01 if len(frame_data) > 1 else 0
                    root_bone.keyframe_insert(data_path="location")
                    
                    if frame_idx == 0:
                        applied_bones += 1
        
        print(f"MMDDriver: 成功应用了{applied_bones}个骨骼的动画")
        return applied_bones > 0


# 全局驱动器实例
_mmd_driver = None

def get_mmd_driver():
    """获取MMD驱动器实例"""
    global _mmd_driver
    if _mmd_driver is None:
        _mmd_driver = MMDDriver()
    return _mmd_driver
