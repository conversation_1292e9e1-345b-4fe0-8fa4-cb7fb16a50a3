# MMD驱动器使用说明

## 概述

本项目已经修复了MMD模型驱动问题，并将驱动逻辑独立到了专门的模块中。现在可以正确地将动作数据应用到Blender中的MMD模型。

## 主要修改

### 1. 创建了独立的MMD驱动模块

- **文件**: `blender_addon/mmd_driver.py`
- **功能**: 专门负责将动作数据应用到MMD模型
- **特性**:
  - 智能骨骼名称映射（支持中文和英文骨骼名称）
  - 坐标系转换（HumanML3D Y-up → Blender Z-up）
  - 多种数据格式支持（22关节3D位置、66维展平数据、272维旋转数据）
  - 自动缩放和位置调整

### 2. 修复了主插件文件

- **文件**: `blender_addon/__init__0.py`
- **修改**:
  - 移除了旧的、有问题的动作应用函数
  - 集成了新的MMD驱动模块
  - 添加了更好的错误处理和日志输出

### 3. 创建了测试工具

- **测试脚本**: `test_mmd_driver.py` - 生成测试动作数据
- **测试插件**: `blender_addon/test_mmd_driver.py` - 在Blender中测试MMD驱动功能

## 使用方法

### 方法1: 使用完整的MotionStreamer插件

1. **安装插件**:
   - 在Blender中，转到 Edit → Preferences → Add-ons
   - 点击 "Install..." 并选择 `blender_addon/__init__0.py`
   - 启用 "MotionStreamer MMD Driver" 插件

2. **加载MMD模型**:
   - 导入一个MMD模型到Blender中
   - 确保模型有骨架对象

3. **连接服务器并生成动作**:
   - 在侧边栏找到 "MotionStreamer" 面板
   - 设置服务器地址和端口
   - 点击 "连接服务器"
   - 输入动作描述文本
   - 选择目标骨架
   - 点击 "生成动作"

### 方法2: 使用测试插件

1. **安装测试插件**:
   - 安装 `blender_addon/test_mmd_driver.py`
   - 启用 "MMD Driver Test" 插件

2. **生成测试数据**:
   - 在侧边栏找到 "MMD Test" 面板
   - 点击 "生成测试数据" 创建测试动作
   - 或者运行 `python test_mmd_driver.py` 生成测试数据

3. **测试动作应用**:
   - 选择目标MMD模型的骨架
   - 点击 "加载测试数据"
   - 观察模型是否开始运动

## 支持的MMD骨骼名称

驱动器支持多种骨骼命名约定：

### 中文骨骼名称（标准MMD）
- センター (center/root)
- 下半身 (lower body)
- 上半身 (upper body)
- 上半身2 (upper body2)
- 首 (neck)
- 頭 (head)
- 左肩/右肩 (left/right shoulder)
- 左腕/右腕 (left/right arm)
- 左ひじ/右ひじ (left/right elbow)
- 左手首/右手首 (left/right wrist)
- 左足/右足 (left/right leg)
- 左ひざ/右ひざ (left/right knee)
- 左足首/右足首 (left/right ankle)

### 英文骨骼名称（备用）
- center, Center, root, Root
- shoulder_L/R, Shoulder_L/R
- arm_L/R, Arm_L/R
- elbow_L/R, Elbow_L/R
- 等等...

## 数据格式支持

### 1. 22关节3D位置数据
- 格式: `[frame][joint][x, y, z]`
- 每帧22个关节，每个关节3个坐标值
- 自动进行坐标系转换和缩放

### 2. 66维展平位置数据
- 格式: `[frame][66_values]`
- 22个关节 × 3个坐标 = 66维
- 自动重新整形为3D位置数据

### 3. 272维旋转数据
- 格式: `[frame][272_values]`
- 包含根骨骼速度、旋转等信息
- 目前主要处理根骨骼移动

## 坐标系转换

驱动器自动处理坐标系转换：

- **HumanML3D**: X-right, Y-up, Z-forward
- **Blender**: X-right, Y-forward, Z-up

转换公式：
```
blender_pos = (
    humanml3d_x,      # X保持不变
    -humanml3d_z,     # Z变为-Y (前后翻转)
    humanml3d_y       # Y变为Z
)
```

## 故障排除

### 1. 模型不动
- 检查是否选择了正确的骨架对象
- 确认骨架有正确的骨骼名称
- 查看Blender控制台的错误信息

### 2. 动作异常
- 检查数据格式是否正确
- 尝试调整缩放因子
- 确认坐标系转换是否正确

### 3. 插件加载失败
- 确保所有依赖文件都在正确位置
- 检查Python控制台的错误信息
- 重新启动Blender并重新加载插件

## 开发说明

### MMDDriver类的主要方法

- `find_armature()`: 查找目标骨架对象
- `find_bone_name()`: 查找实际存在的骨骼名称
- `apply_motion_data()`: 应用动作数据的主入口
- `_apply_position_data()`: 处理位置数据
- `_apply_rotation_data()`: 处理旋转数据

### 扩展支持

要添加新的骨骼名称映射，修改 `mmd_driver.py` 中的：
- `joint_to_mmd_mapping`: 关节到MMD骨骼的映射
- `alternative_bone_names`: 备用骨骼名称列表

## 性能优化

- 使用批量关键帧插入
- 优化坐标转换计算
- 减少不必要的骨骼查找
- 缓存骨骼映射结果

## 未来改进

1. 支持更多MMD模型格式
2. 添加实时预览功能
3. 优化大量帧数据的处理性能
4. 支持更复杂的旋转数据解析
5. 添加动作数据的可视化调试工具
