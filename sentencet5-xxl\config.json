{"_name_or_path": "models/sentence-t5-11b", "architectures": ["T5EncoderModel"], "d_ff": 65536, "d_kv": 128, "d_model": 1024, "decoder_start_token_id": 0, "dropout_rate": 0.1, "eos_token_id": 1, "feed_forward_proj": "relu", "initializer_factor": 1.0, "is_encoder_decoder": true, "layer_norm_epsilon": 1e-06, "model_type": "t5", "n_positions": 512, "num_decoder_layers": 24, "num_heads": 128, "num_layers": 24, "output_past": true, "pad_token_id": 0, "relative_attention_num_buckets": 32, "task_specific_params": {"summarization": {"early_stopping": true, "length_penalty": 2.0, "max_length": 200, "min_length": 30, "no_repeat_ngram_size": 3, "num_beams": 4, "prefix": "summarize: "}, "translation_en_to_de": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to German: "}, "translation_en_to_fr": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to French: "}, "translation_en_to_ro": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to Romanian: "}}, "torch_dtype": "float16", "transformers_version": "4.11.3", "use_cache": true, "vocab_size": 32128}