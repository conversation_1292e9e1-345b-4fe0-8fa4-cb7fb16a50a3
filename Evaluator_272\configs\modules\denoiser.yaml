denoiser:
  target: mld.models.architectures.mld_denoiser.MldDenoiser
  params:
    text_encoded_dim: 768
    ff_size: 1024
    num_layers: 9
    num_heads: 4
    dropout: 0.1
    normalize_before: False
    activation: 'gelu'
    flip_sin_to_cos: True
    return_intermediate_dec: False
    position_embedding: 'learned'
    arch: trans_enc
    freq_shift: 0
    condition: ${model.condition}
    latent_dim: ${model.latent_dim}
    guidance_scale: ${model.guidance_scale}
    guidance_uncondp: ${model.guidance_uncondp}
    nfeats: ${DATASET.NFEATS}
    nclasses: ${DATASET.NCLASSES}
    ablation: ${TRAIN.ABLATION}
