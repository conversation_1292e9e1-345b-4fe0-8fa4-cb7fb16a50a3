#!/usr/bin/env python3
"""
MotionStreamer WebSocket服务器依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装 {package} 失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✓ {package} 已安装")
        return True
    except ImportError:
        print(f"✗ {package} 未安装")
        return False

def main():
    print("MotionStreamer WebSocket服务器依赖检查和安装")
    print("=" * 50)
    
    # 必需的包列表
    required_packages = [
        "websockets",
        "torch",
        "numpy",
        "sentence-transformers",
        "transformers",
        "safetensors",
    ]
    
    # 检查已安装的包
    print("\n1. 检查已安装的包:")
    installed_packages = []
    missing_packages = []
    
    for package in required_packages:
        if check_package(package):
            installed_packages.append(package)
        else:
            missing_packages.append(package)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n2. 安装缺失的包 ({len(missing_packages)} 个):")
        failed_packages = []
        
        for package in missing_packages:
            print(f"\n正在安装 {package}...")
            if not install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\n❌ 以下包安装失败:")
            for package in failed_packages:
                print(f"   - {package}")
            print("\n请手动安装这些包或检查网络连接。")
            return False
    else:
        print("\n✓ 所有必需的包都已安装!")
    
    print("\n3. 验证PyTorch CUDA支持:")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
            print(f"✓ 当前CUDA设备: {torch.cuda.get_device_name()}")
        else:
            print("⚠ CUDA不可用，将使用CPU模式")
    except Exception as e:
        print(f"✗ PyTorch验证失败: {e}")
    
    print("\n4. 检查模型文件:")
    model_files = [
        "Causal_TAE/net_last.pth",
        "Experiments/latest.pth",
        "reference_end_latent_t2m_272.npy",
        "humanml3d_272/mean_std/Mean.npy",
        "humanml3d_272/mean_std/Std.npy",
        "sentencet5-xxl/config.json"
    ]
    
    missing_models = []
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✓ {model_file}")
        else:
            print(f"✗ {model_file} (缺失)")
            missing_models.append(model_file)
    
    if missing_models:
        print(f"\n⚠ 缺失 {len(missing_models)} 个模型文件")
        print("请确保已按照README说明下载所有必需的模型文件。")
    
    print("\n" + "=" * 50)
    if not missing_packages and not missing_models:
        print("🎉 环境配置完成！可以启动WebSocket服务器了。")
        print("\n启动命令:")
        print("python websocket_server.py")
    else:
        print("❌ 环境配置未完成，请解决上述问题后重试。")
    
    return len(missing_packages) == 0 and len(missing_models) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
