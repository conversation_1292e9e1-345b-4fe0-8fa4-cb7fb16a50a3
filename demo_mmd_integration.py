"""
MMD集成演示脚本
展示如何使用修复后的MMD驱动器
"""

import json
import numpy as np
import os

def create_demo_motion_data():
    """创建演示用的动作数据"""
    print("创建演示动作数据...")
    
    # 创建一个简单的走路动作
    frames = 120  # 4秒动画，30fps
    joints = 22   # HumanML3D标准关节数
    
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        t = frame / frames * 4 * np.pi  # 2个完整的步行周期
        
        for joint in range(joints):
            if joint == 0:  # 根关节 - 向前走
                x = frame * 0.03  # 向前移动
                y = 1.0 + np.sin(t * 4) * 0.02  # 轻微上下摆动
                z = 0.0
            elif joint == 1:  # 左髋关节
                x = np.sin(t) * 0.05
                y = 0.8
                z = 0.0
            elif joint == 2:  # 右髋关节
                x = np.sin(t + np.pi) * 0.05
                y = 0.8
                z = 0.0
            elif joint == 16:  # 左腿
                x = 0.0
                y = 0.4 + max(0, np.sin(t)) * 0.15  # 抬腿
                z = np.cos(t) * 0.1
            elif joint == 19:  # 右腿
                x = 0.0
                y = 0.4 + max(0, np.sin(t + np.pi)) * 0.15  # 抬腿
                z = np.cos(t + np.pi) * 0.1
            elif joint == 17:  # 左膝
                x = 0.0
                y = 0.2
                z = max(0, np.sin(t)) * 0.2
            elif joint == 20:  # 右膝
                x = 0.0
                y = 0.2
                z = max(0, np.sin(t + np.pi)) * 0.2
            elif joint == 9:  # 左手臂
                x = np.sin(t + np.pi) * 0.1  # 与右腿同步
                y = 1.2
                z = 0.0
            elif joint == 13:  # 右手臂
                x = np.sin(t) * 0.1  # 与左腿同步
                y = 1.2
                z = 0.0
            elif joint == 7:  # 头部
                x = 0.0
                y = 1.6 + np.sin(t * 4) * 0.01  # 轻微点头
                z = 0.0
            else:  # 其他关节
                x = np.sin(t) * 0.005
                y = 0.5 + joint * 0.1
                z = 0.0
            
            frame_data.append([x, y, z])
        
        motion_data.append(frame_data)
    
    return motion_data

def save_demo_data():
    """保存演示数据"""
    motion_data = create_demo_motion_data()
    
    # 保存为JSON格式
    filename = 'demo_walking_motion.json'
    with open(filename, 'w') as f:
        json.dump(motion_data, f, indent=2)
    
    print(f"演示动作数据已保存到: {filename}")
    print(f"数据统计:")
    print(f"  帧数: {len(motion_data)}")
    print(f"  关节数: {len(motion_data[0])}")
    print(f"  文件大小: {os.path.getsize(filename) / 1024:.1f} KB")
    
    return motion_data, filename

def analyze_motion_data(motion_data):
    """分析动作数据"""
    print("\n动作数据分析:")
    
    frames = len(motion_data)
    joints = len(motion_data[0])
    
    # 计算每个关节的运动范围
    joint_ranges = {}
    for joint_idx in range(joints):
        positions = []
        for frame in motion_data:
            positions.append(frame[joint_idx])
        
        positions = np.array(positions)
        min_pos = np.min(positions, axis=0)
        max_pos = np.max(positions, axis=0)
        range_pos = max_pos - min_pos
        
        joint_ranges[joint_idx] = {
            'min': min_pos.tolist(),
            'max': max_pos.tolist(),
            'range': range_pos.tolist()
        }
    
    # 显示主要关节的运动范围
    important_joints = {
        0: "根关节",
        7: "头部",
        9: "左手臂",
        13: "右手臂",
        16: "左腿",
        19: "右腿"
    }
    
    for joint_idx, joint_name in important_joints.items():
        if joint_idx < joints:
            range_info = joint_ranges[joint_idx]
            print(f"  {joint_name} (关节{joint_idx}):")
            print(f"    X范围: {range_info['range'][0]:.3f}")
            print(f"    Y范围: {range_info['range'][1]:.3f}")
            print(f"    Z范围: {range_info['range'][2]:.3f}")

def create_blender_instructions():
    """创建Blender使用说明"""
    instructions = """
# Blender中使用MMD驱动器的步骤

## 1. 准备工作
1. 打开Blender
2. 导入一个MMD模型（.pmx文件）
3. 确保模型有骨架对象

## 2. 安装插件
1. 转到 Edit → Preferences → Add-ons
2. 点击 "Install..." 
3. 选择 blender_addon/__init__0.py 文件
4. 启用 "MotionStreamer MMD Driver" 插件

## 3. 测试动作应用
### 方法A: 使用测试插件
1. 安装 blender_addon/test_mmd_driver.py
2. 在侧边栏找到 "MMD Test" 面板
3. 选择目标骨架
4. 点击 "生成测试数据"
5. 点击 "加载测试数据"

### 方法B: 使用完整功能
1. 启动 websocket_server.py
2. 在 "MotionStreamer" 面板中连接服务器
3. 输入动作描述
4. 选择目标骨架
5. 点击 "生成动作"

## 4. 预期结果
- 模型应该开始执行走路动作
- 根关节向前移动
- 腿部交替抬起
- 手臂摆动
- 头部轻微摆动

## 5. 故障排除
- 如果模型不动，检查骨架选择是否正确
- 查看Blender控制台的错误信息
- 确认动作数据格式正确
"""
    
    with open('BLENDER_USAGE_INSTRUCTIONS.md', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("Blender使用说明已保存到: BLENDER_USAGE_INSTRUCTIONS.md")

def main():
    """主函数"""
    print("=== MMD集成演示 ===")
    
    # 创建并保存演示数据
    motion_data, filename = save_demo_data()
    
    # 分析动作数据
    analyze_motion_data(motion_data)
    
    # 创建使用说明
    create_blender_instructions()
    
    print(f"\n=== 完成 ===")
    print(f"演示文件已创建:")
    print(f"  - {filename} (动作数据)")
    print(f"  - BLENDER_USAGE_INSTRUCTIONS.md (使用说明)")
    print(f"  - MMD_DRIVER_USAGE.md (详细文档)")
    
    print(f"\n下一步:")
    print(f"1. 在Blender中加载MMD模型")
    print(f"2. 安装并启用MotionStreamer插件")
    print(f"3. 使用测试插件加载 {filename}")
    print(f"4. 观察模型是否正确执行走路动作")

if __name__ == "__main__":
    main()
